# Backend Setup Guide

This guide will help you set up and run the VPS Admin backend server to resolve connection issues.

## Quick Fix for CORS Errors

If you're seeing CORS errors like "Access to fetch at 'http://localhost:8000/' from origin 'http://localhost:5173' has been blocked by CORS policy", use the automated restart script:

```bash
restart-backend.bat
```

This script will:
1. Stop any existing backend servers
2. Restart with updated CORS configuration
3. Install any missing dependencies

## Manual Setup

If the automated script doesn't work, follow these steps:

### 1. Open a New Terminal/Command Prompt

Navigate to the backend directory:
```bash
cd Desktop\github-workflow\vps-admin-frontend\backend
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Start the Backend Server

```bash
python main.py
```

You should see output like:
```
Setting up modular VPS AI Admin Backend...
Loading environment variables...
Configuration setup completed.
Initializing modular components...
...
INFO:     Uvicorn running on http://0.0.0.0:8000
```

### 4. Keep the Terminal Open

Leave this terminal window open while using the frontend. The backend server needs to stay running.

## Troubleshooting

### Python Not Found
If you get "python is not recognized":
- Install Python from [python.org](https://python.org)
- Or try `python3` instead of `python`

### Permission Errors
If you get permission errors:
```bash
python -m pip install -r requirements.txt
```

### Port Already in Use
If port 8000 is already in use:
1. Find the process using port 8000:
   ```bash
   netstat -ano | findstr :8000
   ```
2. Kill the process or restart your computer

### Environment Variables
Make sure the `.env` file exists in the backend directory with:
```
GEMINI_API_KEY=your_api_key_here
VPS_HOSTNAME=localhost
VPS_PORT=4646
VPS_USERNAME=root
VPS_PASSWORD=root
```

## Verification

Once the backend is running:
1. The frontend should automatically detect the connection
2. You can test manually by visiting: http://localhost:8000
3. You should see a JSON response with server information

### Test CORS Configuration

Open `test-cors.html` in your browser to verify CORS is working:
1. Open the file in any web browser
2. Click "Test Health Check" - should show success
3. Click "Test Start Task" - should show success (or a backend error, but not CORS error)
4. Click "Test OPTIONS Request" - should show CORS headers

If any tests show CORS errors, restart the backend using `restart-backend.bat`.

## Need Help?

If you're still having issues:
1. Check the backend terminal for error messages
2. Use the "Backend Status" feature in the frontend for diagnostics
3. Follow the step-by-step troubleshooting guide in the frontend

## Architecture

The system consists of:
- **Frontend**: React app running on port 5173 (Vite dev server)
- **Backend**: FastAPI server running on port 8000
- **Communication**: REST API + Server-Sent Events (SSE) for real-time updates

Both need to be running simultaneously for the application to work properly.
