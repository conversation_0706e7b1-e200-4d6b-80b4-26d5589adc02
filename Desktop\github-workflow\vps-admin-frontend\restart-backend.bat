@echo off
echo ========================================
echo VPS Admin Backend Restart Script
echo ========================================
echo.

echo Stopping any existing backend servers on port 8000...

REM Kill any processes using port 8000
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8000') do (
    echo Killing process %%a
    taskkill /F /PID %%a >nul 2>&1
)

echo Waiting for processes to stop...
timeout /t 3 /nobreak >nul

echo.
echo Checking if backend directory exists...
if not exist "backend" (
    echo ERROR: Backend directory not found!
    echo Please run this script from the vps-admin-frontend directory.
    pause
    exit /b 1
)

echo Navigating to backend directory...
cd backend

echo.
echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found. Trying python3...
    python3 --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ERROR: Python is not installed or not in PATH.
        echo Please install Python from https://python.org
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=python3
    )
) else (
    set PYTHON_CMD=python
)

echo Python found: %PYTHON_CMD%
echo.

echo Installing/updating dependencies...
%PYTHON_CMD% -m pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo WARNING: Failed to install some dependencies.
    echo Continuing anyway...
)

echo.
echo Starting backend server with updated CORS configuration...
echo.
echo ========================================
echo Backend server is starting...
echo The CORS configuration has been updated.
echo Keep this window open while using the frontend.
echo Press Ctrl+C to stop the server.
echo ========================================
echo.

%PYTHON_CMD% main.py

echo.
echo Backend server stopped.
pause
