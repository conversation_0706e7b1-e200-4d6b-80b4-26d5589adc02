#!/usr/bin/env python3
"""
Test script to verify the orchestrator's error recovery functionality.
This specifically tests the "already exists" error scenario.
"""

import asyncio
import json
from config import Config
from orchestrator import TaskOrchestrator
from ssh_client import SSHClient
from models import TaskEntry, TaskPlan, TaskStep, SSHResult, StepStatus


class MockSSHClient:
    """Mock SSH client that simulates the 'already exists' error."""
    
    def __init__(self):
        self.call_count = 0
        
    async def execute_command_async(self, command: str) -> SSHResult:
        """Simulate SSH command execution with 'already exists' error."""
        self.call_count += 1
        
        print(f"[MockSSH] Executing command (call #{self.call_count}): {command}")
        
        # Simulate git clone failure due to directory already existing
        if "git clone" in command and "/var/www/html/portfolio" in command:
            return SSHResult(
                stdout="",
                stderr="fatal: destination path '/var/www/html/portfolio' already exists and is not an empty directory.",
                exit_status=128,
                success=False,
                command=command,
                execution_time=100
            )
        
        # Simulate successful rm command
        elif "rm -rf" in command and "/var/www/html/portfolio" in command:
            return SSHResult(
                stdout="",
                stderr="",
                exit_status=0,
                success=True,
                command=command,
                execution_time=50
            )
        
        # Simulate successful git clone after cleanup
        elif "git clone" in command and self.call_count > 2:
            return SSHResult(
                stdout="Cloning into '/var/www/html/portfolio'...\nremote: Enumerating objects: 100, done.",
                stderr="",
                exit_status=0,
                success=True,
                command=command,
                execution_time=2000
            )
        
        # Simulate successful chown command
        elif "chown" in command:
            return SSHResult(
                stdout="",
                stderr="",
                exit_status=0,
                success=True,
                command=command,
                execution_time=30
            )
        
        # Default success for other commands
        else:
            return SSHResult(
                stdout="Command executed successfully",
                stderr="",
                exit_status=0,
                success=True,
                command=command,
                execution_time=100
            )


async def test_orchestrator_error_recovery():
    """Test the orchestrator's error recovery for 'already exists' scenario."""
    print("\n=== Testing Orchestrator Error Recovery ===")
    
    # Setup
    config = Config()
    mock_ssh = MockSSHClient()
    orchestrator = TaskOrchestrator(config, mock_ssh)
    
    # Create a task with a git clone step that will fail
    task = TaskEntry(
        id="test-recovery-001",
        title="Test Error Recovery",
        description="Test git clone error recovery",
        priority="medium",
        history=[],
        status="PLANNED",
        command_to_confirm=None,
        system_info="Ubuntu 22.04 LTS",
        created_at=asyncio.get_event_loop().time(),
        use_orchestrator=True,
        max_retries_per_step=3
    )
    
    # Create a task plan with a git clone step
    step = TaskStep(
        step_number=1,
        description="Clone portfolio repository to web directory",
        command="git clone https://github.com/user/portfolio /var/www/html/portfolio",
        status=StepStatus.PENDING
    )
    
    task.task_plan = TaskPlan(
        steps=[step],
        total_steps=1,
        current_step=0
    )
    
    print(f"Created test task: {task.id}")
    print(f"Step command: {step.command}")
    
    # Test the error recovery flow
    print("\n--- Testing Error Recovery Flow ---")
    
    try:
        # Execute the step that will fail and trigger recovery
        events = []
        async for event in orchestrator._execute_confirmed_command(task, step.command, step, 3):  # Simulate max attempts
            event_data = json.loads(event.data)
            events.append(event_data)
            print(f"Event: {event_data['type']} - {event_data.get('content', '')[:100]}")
        
        # Check if error recovery was triggered
        recovery_events = [e for e in events if e['type'] == 'error_recovery_start']
        if recovery_events:
            print("✅ Error recovery was triggered successfully!")
            print(f"Recovery event: {recovery_events[0]['content']}")
        else:
            print("❌ Error recovery was NOT triggered")
            print("Available events:")
            for event in events:
                print(f"  - {event['type']}: {event.get('content', '')}")
        
        # Test the recovery planner directly
        print("\n--- Testing Recovery Plan Creation ---")
        
        ssh_result = SSHResult(
            stdout="",
            stderr="fatal: destination path '/var/www/html/portfolio' already exists and is not an empty directory.",
            exit_status=128,
            success=False,
            command="git clone https://github.com/user/portfolio /var/www/html/portfolio"
        )
        
        # Check if recovery should be triggered
        should_recover = orchestrator._should_trigger_error_recovery(ssh_result)
        print(f"Should trigger recovery: {should_recover}")
        
        if should_recover:
            # Test recovery plan creation
            recovery_events = []
            async for event in orchestrator._trigger_error_recovery(task, step, step.command, ssh_result):
                event_data = json.loads(event.data)
                recovery_events.append(event_data)
                print(f"Recovery Event: {event_data['type']} - {event_data.get('content', '')[:100]}")
            
            # Check for recovery plan
            plan_events = [e for e in recovery_events if e['type'] == 'error_recovery_plan']
            if plan_events:
                print("✅ Recovery plan created successfully!")
                metadata = plan_events[0].get('metadata', {})
                print(f"Recovery steps: {metadata.get('recoverySteps', 0)}")
                print(f"Recovery approach: {metadata.get('recoveryApproach', 'unknown')}")
            else:
                print("❌ Recovery plan was NOT created")
        
        print("\n=== Test Summary ===")
        print(f"SSH calls made: {mock_ssh.call_count}")
        print(f"Task status: {task.status}")
        print(f"Step status: {step.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the error recovery test."""
    print("Starting Orchestrator Error Recovery Test...")
    
    success = await test_orchestrator_error_recovery()
    
    if success:
        print("\n🎉 Orchestrator Error Recovery Test PASSED!")
    else:
        print("\n❌ Orchestrator Error Recovery Test FAILED!")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
