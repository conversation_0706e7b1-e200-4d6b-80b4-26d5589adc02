<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test for VPS Admin Backend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>VPS Admin Backend CORS Test</h1>
        <p>This page tests if the backend server is running and CORS is configured correctly.</p>
        
        <div>
            <button onclick="testHealthCheck()">Test Health Check (GET /)</button>
            <button onclick="testStartTask()">Test Start Task (POST /start_task)</button>
            <button onclick="testOptions()">Test OPTIONS Request</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testHealthCheck() {
            addResult('Testing health check endpoint...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    },
                    mode: 'cors',
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Health check successful: ${JSON.stringify(data)}`, 'success');
                } else {
                    addResult(`❌ Health check failed: HTTP ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Health check error: ${error.message}`, 'error');
                if (error.message.includes('CORS')) {
                    addResult('💡 CORS error detected. Make sure the backend server is running with updated CORS configuration.', 'info');
                }
            }
        }
        
        async function testStartTask() {
            addResult('Testing start task endpoint...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/start_task`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        initial_prompt: 'test prompt for CORS verification'
                    }),
                    mode: 'cors',
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Start task successful: ${JSON.stringify(data)}`, 'success');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Start task failed: HTTP ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Start task error: ${error.message}`, 'error');
                if (error.message.includes('CORS')) {
                    addResult('💡 CORS error detected. The backend CORS configuration may need updating.', 'info');
                }
            }
        }
        
        async function testOptions() {
            addResult('Testing OPTIONS preflight request...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/start_task`, {
                    method: 'OPTIONS',
                    headers: {
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type',
                    },
                    mode: 'cors',
                });
                
                addResult(`✅ OPTIONS request successful: HTTP ${response.status}`, 'success');
                
                // Check CORS headers
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                };
                
                addResult(`CORS Headers: ${JSON.stringify(corsHeaders, null, 2)}`, 'info');
                
            } catch (error) {
                addResult(`❌ OPTIONS request error: ${error.message}`, 'error');
            }
        }
        
        // Auto-run health check on page load
        window.addEventListener('load', () => {
            addResult('Page loaded. Click buttons above to test backend connectivity.', 'info');
        });
    </script>
</body>
</html>
