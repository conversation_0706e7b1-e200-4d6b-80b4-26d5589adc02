/**
 * ChatInput component for message input with command history
 */

import React, { useState, useRef, useEffect } from 'react';
import { Send, History } from 'lucide-react';
import { ChatInputProps } from '../types';
import { COMMON_COMMANDS } from '../constants';
import { findCommandCompletions } from '../utils';

const ChatInput: React.FC<ChatInputProps> = ({
  value,
  onChange,
  onSubmit,
  onKeyDown,
  disabled,
  placeholder,
  commandHistory,
  historyIndex
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [selectedSuggestion, setSelectedSuggestion] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);

  // Update suggestions when input changes
  useEffect(() => {
    if (value.trim().length > 0) {
      const historySuggestions = findCommandCompletions(value, commandHistory);
      const commonSuggestions = findCommandCompletions(value, COMMON_COMMANDS);
      const allSuggestions = [...new Set([...historySuggestions, ...commonSuggestions])];

      setSuggestions(allSuggestions.slice(0, 5)); // Limit to 5 suggestions
      setShowSuggestions(allSuggestions.length > 0);
      setSelectedSuggestion(-1);
    } else {
      setShowSuggestions(false);
      setSuggestions([]);
    }
  }, [value, commandHistory]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Handle suggestions navigation
    if (showSuggestions && suggestions.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedSuggestion(prev =>
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        return;
      }

      if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedSuggestion(prev =>
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        return;
      }

      if (e.key === 'Tab' && selectedSuggestion >= 0) {
        e.preventDefault();
        onChange(suggestions[selectedSuggestion]);
        setShowSuggestions(false);
        return;
      }

      if (e.key === 'Escape') {
        e.preventDefault();
        setShowSuggestions(false);
        setSelectedSuggestion(-1);
        return;
      }
    }

    // Pass through to parent handler for history navigation and submit
    onKeyDown(e);
  };

  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const handleSubmit = () => {
    if (showSuggestions && selectedSuggestion >= 0) {
      onChange(suggestions[selectedSuggestion]);
      setShowSuggestions(false);
    } else {
      onSubmit();
    }
  };

  return (
    <div className="relative">
      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute bottom-full left-0 right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-40 overflow-y-auto z-50">
          {suggestions.map((suggestion, index) => (
            <div
              key={suggestion}
              className={`px-3 py-2 cursor-pointer text-sm border-b border-gray-100 last:border-b-0 ${
                index === selectedSuggestion
                  ? 'bg-blue-50 text-blue-700'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <div className="flex items-center gap-2">
                {commandHistory.includes(suggestion) ? (
                  <History size={12} className="text-blue-500" />
                ) : (
                  <span className="w-3 h-3 bg-gray-300 rounded-full flex-shrink-0" />
                )}
                <span className="truncate">{suggestion}</span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Input container */}
      <div className="flex items-center gap-2">
        {/* History indicator */}
        {historyIndex >= 0 && (
          <div className="flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
            <History size={12} />
            <span>{historyIndex + 1}/{commandHistory.length}</span>
          </div>
        )}

        {/* Input field */}
        <div className="flex-1 relative">
          <input
            ref={inputRef}
            type="text"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={disabled ? "Task in progress..." : placeholder}
            disabled={disabled}
            className="w-full px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed text-base"
          />

          {/* Keyboard shortcuts hint */}
          {!disabled && value.length === 0 && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2 text-xs text-gray-400">
              <span className="hidden lg:inline">↑↓ History</span>
              <span className="hidden lg:inline">Tab Complete</span>
            </div>
          )}
        </div>

        {/* Submit button */}
        <button
          onClick={handleSubmit}
          disabled={disabled || value.trim().length === 0}
          className="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <Send size={16} />
        </button>
      </div>

      {/* Help text */}
      {!disabled && (
        <div className="px-3 py-1 text-xs text-gray-500 bg-gray-50 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <span>
              Press <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Enter</kbd> to send
            </span>
            <span className="hidden sm:inline">
              <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">↑</kbd>
              <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">↓</kbd> for history
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatInput;
