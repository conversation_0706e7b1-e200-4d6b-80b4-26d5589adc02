#!/usr/bin/env python3
"""
Test script to verify the API and orchestrator are working correctly.
"""

import requests
import json
import time

def test_api_request():
    """Test the API with a portfolio hosting request."""

    print("=== Testing API Request ===")

    # Start a new task
    start_url = "http://localhost:8000/start_task"
    start_data = {
        "initial_prompt": "host this static web app: https://github.com/Moetez-Baklouti/portfolio"
    }
    start_response = requests.post(start_url, json=start_data)

    if start_response.status_code != 201:
        print(f"❌ Failed to start task: {start_response.status_code}")
        return False

    task_data = start_response.json()
    task_id = task_data['task_id']
    print(f"✅ Task started with ID: {task_id}")

    # Send the message (this will trigger the orchestrator)
    message_url = "http://localhost:8000/send_message"
    message_data = {
        "task_id": task_id,
        "message": "start execution"
    }

    message_response = requests.post(message_url, json=message_data)

    if message_response.status_code != 200:
        print(f"❌ Failed to send message: {message_response.status_code}")
        return False

    print("✅ Message sent successfully")

    # Wait a bit for processing
    time.sleep(2)

    # Check task status
    status_url = f"http://localhost:8000/task/{task_id}"
    status_response = requests.get(status_url)

    if status_response.status_code != 200:
        print(f"❌ Failed to get task status: {status_response.status_code}")
        return False

    status_data = status_response.json()
    print(f"✅ Task status: {status_data['status']}")

    if 'task_plan' in status_data and status_data['task_plan']:
        steps = status_data['task_plan']['steps']
        print(f"✅ Plan created with {len(steps)} steps:")
        for i, step in enumerate(steps, 1):
            print(f"  {i}. {step['description']}")

    return True

if __name__ == "__main__":
    success = test_api_request()
    if success:
        print("\n🎉 API and orchestrator are working correctly!")
    else:
        print("\n❌ API test failed")
