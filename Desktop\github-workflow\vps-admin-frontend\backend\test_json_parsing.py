#!/usr/bin/env python3
"""
Test script to verify JSON parsing fix for markdown-wrapped responses.
"""

import json

def test_json_parsing():
    """Test the JSON parsing logic with markdown-wrapped content."""
    
    # Simulate the response that was causing the issue
    response_text = """```json
[
  {"description": "Ensure the Apache web server is running and enabled to start on boot."},
  {"description": "Create a dedicated directory for the static web application files within the Apache web root."},
  {"description": "Clone the static web application repository into a temporary location."},
  {"description": "Copy the contents of the cloned repository to the designated web application directory."},
  {"description": "Set appropriate ownership and permissions for the web application files and directory to be accessible by the web server."},
  {"description": "Create a new Apache Virtual Host configuration file for the static web application."},
  {"description": "Enable the newly created Apache Virtual Host."},
  {"description": "Disable the default Apache Virtual Host to ensure only the new site is served (optional but recommended)."},
  {"description": "Test the Apache configuration for syntax errors."},
  {"description": "Reload the Apache web server to apply the new configuration."},
  {"description": "Verify the Apache web server is running and the web application is accessible."}
]
```"""
    
    print("Original response:")
    print(response_text)
    print("\n" + "="*50 + "\n")
    
    # Apply the same cleaning logic as in the PlannerAgent
    cleaned_response = response_text.strip()
    if cleaned_response.startswith('```json'):
        # Remove ```json from start and ``` from end
        cleaned_response = cleaned_response[7:]  # Remove ```json
        if cleaned_response.endswith('```'):
            cleaned_response = cleaned_response[:-3]  # Remove ```
    elif cleaned_response.startswith('```'):
        # Remove ``` from start and end
        cleaned_response = cleaned_response[3:]
        if cleaned_response.endswith('```'):
            cleaned_response = cleaned_response[:-3]
    
    cleaned_response = cleaned_response.strip()
    
    print("Cleaned response:")
    print(cleaned_response)
    print("\n" + "="*50 + "\n")
    
    try:
        plan_data = json.loads(cleaned_response)
        print("✅ JSON parsing successful!")
        print(f"Number of steps: {len(plan_data)}")
        print("\nParsed steps:")
        for i, step in enumerate(plan_data, 1):
            print(f"  {i}. {step['description']}")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing failed: {e}")
        return False

if __name__ == "__main__":
    success = test_json_parsing()
    if success:
        print("\n🎉 JSON parsing fix is working correctly!")
    else:
        print("\n❌ JSON parsing fix needs more work.")
