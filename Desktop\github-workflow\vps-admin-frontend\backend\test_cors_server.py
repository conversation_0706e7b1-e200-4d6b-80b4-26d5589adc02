#!/usr/bin/env python3
"""
Minimal CORS test server to verify CORS configuration works
Run this if the main backend has issues
"""

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Create FastAPI app
app = FastAPI(title="CORS Test Server", version="1.0.0")

# Configure CORS - Very permissive for testing
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=False,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "CORS Test Server is running",
        "status": "ok",
        "cors": "enabled"
    }

@app.post("/start_task")
async def start_task(request: dict):
    """Test endpoint for start_task"""
    return {
        "task_id": "test-task-123",
        "message": "CORS test successful",
        "received": request
    }

@app.post("/send_message")
async def send_message(request: dict):
    """Test endpoint for send_message"""
    return {
        "message": "CORS test successful for send_message",
        "received": request
    }

@app.options("/{path:path}")
async def options_handler(path: str):
    """Handle OPTIONS requests for CORS preflight"""
    return {"message": "OPTIONS OK"}

if __name__ == "__main__":
    print("=" * 50)
    print("CORS Test Server Starting...")
    print("This is a minimal server to test CORS configuration")
    print("If this works, the issue is with the main backend")
    print("If this doesn't work, the issue is with your environment")
    print("=" * 50)
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        log_level="info"
    )
