/**
 * Command history management hook for VPS Admin Chat
 */

import { useState, useCallback } from 'react';
import { UseCommandHistoryReturn } from '../types';
import { filterCommandHistory, limitArray } from '../utils';
import { MAX_COMMAND_HISTORY } from '../constants';

export const useCommandHistory = (): UseCommandHistoryReturn => {
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState<number>(-1);

  const addToCommandHistory = useCallback((command: string) => {
    if (!command.trim()) return;

    setCommandHistory(prev => {
      const filtered = filterCommandHistory(prev, command);
      return limitArray(filtered, MAX_COMMAND_HISTORY);
    });
    setHistoryIndex(-1);
  }, []);

  const navigateHistory = useCallback((direction: 'up' | 'down'): string => {
    if (commandHistory.length === 0) return '';

    let newIndex = historyIndex;

    if (direction === 'up') {
      newIndex = Math.min(historyIndex + 1, commandHistory.length - 1);
    } else if (direction === 'down') {
      newIndex = Math.max(historyIndex - 1, -1);
    }

    setHistoryIndex(newIndex);
    return newIndex >= 0 ? commandHistory[newIndex] : '';
  }, [commandHistory, historyIndex]);

  const clearHistory = useCallback(() => {
    setCommandHistory([]);
    setHistoryIndex(-1);
  }, []);

  return {
    // State
    commandHistory,
    historyIndex,

    // Core operations
    addToCommandHistory,
    navigateHistory,
    setHistoryIndex,
    clearHistory
  };
};
