@echo off
echo ========================================
echo CORS Fix Script - Immediate Solution
echo ========================================
echo.

echo Step 1: Killing all Python processes...
taskkill /F /IM python.exe /T >nul 2>&1
taskkill /F /IM python3.exe /T >nul 2>&1

echo Step 2: Waiting for processes to stop...
timeout /t 3 /nobreak >nul

echo Step 3: Checking if port 8000 is free...
netstat -ano | findstr :8000 >nul
if %errorlevel% equ 0 (
    echo Port 8000 is still in use. Trying to free it...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8000') do (
        taskkill /F /PID %%a >nul 2>&1
    )
    timeout /t 2 /nobreak >nul
)

echo Step 4: Navigating to backend directory...
if not exist "backend" (
    echo ERROR: Backend directory not found!
    echo Please run this script from the vps-admin-frontend directory.
    pause
    exit /b 1
)

cd backend

echo Step 5: Starting backend with CORS fix...
echo.
echo ========================================
echo Backend is starting with CORS fix...
echo You should see "CORS middleware configured" message.
echo Keep this window open!
echo ========================================
echo.

python main.py

pause
