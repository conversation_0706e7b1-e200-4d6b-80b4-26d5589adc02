"""
System scanner module for VPS AI Admin Backend.
Handles initial system scanning, OS detection, and service discovery.
"""

from typing import Dict, Any
from ssh_client import SSHClient


class SystemScanner:
    """System scanner for gathering remote system information."""
    
    def __init__(self, ssh_client: SSHClient):
        self.ssh_client = ssh_client
    
    async def perform_initial_scan(self) -> str:
        """
        Performs an initial scan of the remote system to gather OS info,
        running services, and installed packages. Attempts common commands.
        """
        print("INFO: Performing enhanced initial system scan...")
        scan_results = {
            "os_info": "OS detection failed.",
            "services": "Could not list services.",
            "packages": "Could not list packages."
        }
        os_detected = ""  # Store detected OS ID for command selection
        
        # 1. Get OS Info
        os_detected = await self._scan_os_info(scan_results)
        
        # 2. Get Running Services
        await self._scan_services(scan_results, os_detected)
        
        # 3. Get Installed Packages
        await self._scan_packages(scan_results, os_detected)
        
        # 4. Combine results into a single string
        final_info = (
            f"--- System Scan Results ---\n"
            f"**OS:** {scan_results['os_info']}\n\n"
            f"**Running Services:**\n{scan_results['services']}\n\n"
            f"**Installed Packages:**\n{scan_results['packages']}\n"
            f"--- End System Scan Results ---"
        )
        print("INFO: Enhanced initial scan complete.")
        return final_info
    
    async def _scan_os_info(self, scan_results: Dict[str, str]) -> str:
        """Scan OS information and return detected OS ID."""
        os_command = "cat /etc/os-release 2>/dev/null || uname -a"
        print(f"INFO: Scanning OS: '{os_command}'")
        os_detected = ""
        
        try:
            os_result = await self.ssh_client.execute_command_async(os_command)
            if os_result.success and os_result.stdout.strip():
                output = os_result.stdout.strip()
                scan_results["os_info"] = f"Raw OS Info:\n{output}"  # Default if pretty name fails
                pretty_name = ""
                os_id = ""
                
                try:  # Try parsing /etc/os-release format
                    lines = output.splitlines()
                    for line in lines:
                        if line.startswith("PRETTY_NAME="):
                            pretty_name = line.split("=", 1)[1].strip('" ')
                        elif line.startswith("ID="):
                            os_id = line.split("=", 1)[1].strip('" ')
                    
                    if pretty_name:
                        scan_results["os_info"] = f"OS Detected: {pretty_name}"
                        os_detected = os_id.lower() if os_id else ""  # Store lowercased ID
                        print(f"INFO: OS Scan OK: {pretty_name} (ID: {os_detected})")
                    else:  # Fallback if PRETTY_NAME not found but command succeeded
                        scan_results["os_info"] = f"OS Info (uname):\n{output}"
                        print("INFO: OS Scan OK (using uname output).")
                
                except Exception as parse_err:
                    print(f"WARNING: Could not parse OS info output: {parse_err}")
                    # Keep the raw output as os_info
            elif not os_result.success:
                scan_results["os_info"] = f"OS detection command failed. Exit: {os_result.exit_status}. Stderr: {os_result.stderr}"
                print(f"ERROR: OS Scan command failed: {scan_results['os_info']}")
            else:
                scan_results["os_info"] = "OS detection command ran but produced no output."
                print("WARNING: OS Scan command returned no output.")
        
        except Exception as e:
            scan_results["os_info"] = f"Exception during OS scan: {type(e).__name__}: {e}"
            print(f"ERROR: OS Scan exception: {scan_results['os_info']}")
        
        return os_detected
    
    async def _scan_services(self, scan_results: Dict[str, str], os_detected: str):
        """Scan running services."""
        service_command = ""
        
        # Prefer systemctl if OS likely uses it or if unknown
        if os_detected in ["ubuntu", "debian", "centos", "fedora", "rhel"] or not os_detected:
            service_command = "systemctl list-units --type=service --state=running --no-pager --plain"
            print(f"INFO: Scanning Services (systemd): '{service_command}'")
        else:  # Fallback for potentially older systems
            service_command = "service --status-all 2>/dev/null || ps aux"  # ps aux as last resort
            print(f"INFO: Scanning Services (service/ps): '{service_command}'")
        
        try:
            service_result = await self.ssh_client.execute_command_async(service_command)
            if service_result.success and service_result.stdout.strip():
                # Limit output size for context
                service_list = "\n".join(service_result.stdout.strip().splitlines()[:50])  # Limit to 50 lines
                scan_results["services"] = f"Running Services (limited):\n{service_list}"
                print(f"INFO: Service Scan OK (using '{service_command.split()[0]}').")
            # If systemctl failed, maybe try 'service' if we didn't already
            elif "systemctl" in service_command and not service_result.success:
                print("WARNING: systemctl command failed. Trying 'service --status-all'.")
                service_command = "service --status-all 2>/dev/null"
                service_result = await self.ssh_client.execute_command_async(service_command)
                if service_result.success and service_result.stdout.strip():
                    service_list = "\n".join(service_result.stdout.strip().splitlines()[:50])
                    scan_results["services"] = f"Running Services (via service, limited):\n{service_list}"
                    print("INFO: Service Scan OK (using 'service').")
                elif not service_result.success:
                    scan_results["services"] = f"Service listing failed (systemctl & service). Stderr: {service_result.stderr}"
                    print(f"ERROR: Service Scan failed (both methods): {scan_results['services']}")
                else:
                    scan_results["services"] = "Service listing command (service) ran but produced no output."
                    print("WARNING: Service Scan command (service) returned no output.")
            elif not service_result.success:
                scan_results["services"] = f"Service listing failed. Exit: {service_result.exit_status}. Stderr: {service_result.stderr}"
                print(f"ERROR: Service Scan failed: {scan_results['services']}")
            else:  # Command succeeded but no output
                scan_results["services"] = "Service listing command ran but produced no output."
                print("WARNING: Service Scan command returned no output.")
        except Exception as e:
            scan_results["services"] = f"Exception during service scan: {type(e).__name__}: {e}"
            print(f"ERROR: Service Scan exception: {scan_results['services']}")
    
    async def _scan_packages(self, scan_results: Dict[str, str], os_detected: str):
        """Scan installed packages."""
        package_command = ""
        
        if os_detected in ["debian", "ubuntu"]:
            package_command = "dpkg-query -W -f='${Package}\\n'"  # Get only package names
            print(f"INFO: Scanning Packages (dpkg): '{package_command}'")
        elif os_detected in ["centos", "fedora", "rhel"]:
            package_command = "rpm -qa --qf '%{NAME}\\n'"  # Get only package names
            print(f"INFO: Scanning Packages (rpm): '{package_command}'")
        else:  # If OS unknown, try dpkg first as it's common
            package_command = "dpkg-query -W -f='${Package}\\n' 2>/dev/null"
            print(f"INFO: Scanning Packages (dpkg - fallback): '{package_command}'")
        
        try:
            package_result = await self.ssh_client.execute_command_async(package_command)
            # Check if dpkg succeeded (even if OS wasn't detected as debian/ubuntu)
            if package_result.success and package_result.stdout.strip():
                package_list = "\n".join(package_result.stdout.strip().splitlines()[:100])  # Limit to 100 lines
                scan_results["packages"] = f"Installed Packages (dpkg, limited):\n{package_list}"
                print("INFO: Package Scan OK (using dpkg).")
            # If dpkg failed (or wasn't the primary choice) and OS is unknown or RPM-based, try rpm
            elif (not package_result.success or not package_result.stdout.strip()) and (not os_detected or os_detected in ["centos", "fedora", "rhel"]):
                print("INFO: dpkg failed or empty, trying rpm.")
                package_command = "rpm -qa --qf '%{NAME}\\n' 2>/dev/null"
                package_result = await self.ssh_client.execute_command_async(package_command)
                if package_result.success and package_result.stdout.strip():
                    package_list = "\n".join(package_result.stdout.strip().splitlines()[:100])
                    scan_results["packages"] = f"Installed Packages (rpm, limited):\n{package_list}"
                    print("INFO: Package Scan OK (using rpm).")
                elif not package_result.success:
                    scan_results["packages"] = f"Package listing failed (dpkg & rpm). Stderr: {package_result.stderr}"
                    print(f"ERROR: Package Scan failed (both methods): {scan_results['packages']}")
                else:
                    scan_results["packages"] = "Package listing command (rpm) ran but produced no output."
                    print("WARNING: Package Scan command (rpm) returned no output.")
            elif not package_result.success:  # Initial command failed (and wasn't retried with rpm)
                scan_results["packages"] = f"Package listing failed. Exit: {package_result.exit_status}. Stderr: {package_result.stderr}"
                print(f"ERROR: Package Scan failed: {scan_results['packages']}")
            else:  # Initial command succeeded but no output
                scan_results["packages"] = "Package listing command ran but produced no output."
                print("WARNING: Package Scan command returned no output.")
        
        except Exception as e:
            scan_results["packages"] = f"Exception during package scan: {type(e).__name__}: {e}"
            print(f"ERROR: Package Scan exception: {scan_results['packages']}")
