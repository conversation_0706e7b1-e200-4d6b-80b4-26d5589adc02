import traceback
import warnings

# Suppress cryptography deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="paramiko")
warnings.filterwarnings("ignore", message=".*TripleDES.*", category=DeprecationWarning)

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from sse_starlette.sse import EventSourceResponse

# Import modular components
from config import setup_configuration
from models import StartRequest, MessageRequest, TaskStatusResponse
from task_manager import TaskManager
from stream_processor import StreamProcessor

# --- Configuration & Setup ---
print("Setting up modular VPS AI Admin Backend...")
config = setup_configuration()

# --- Initialize Components ---
print("Initializing modular components...")
task_manager = TaskManager(config)
stream_processor = StreamProcessor(task_manager, config)

# --- FastAPI App Setup ---
print("Setting up FastAPI application...")
app = FastAPI(title=config.APP_TITLE, version=config.APP_VERSION)

# Configure CORS middleware with explicit settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",  # Vite dev server
        "http://127.0.0.1:5173",  # Alternative localhost
        "http://localhost:3000",  # React dev server (if used)
        "http://127.0.0.1:3000",  # Alternative localhost
        "*"  # Allow all origins for development
    ],
    allow_credentials=False,  # Set to False to avoid credential issues
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "Origin",
        "Access-Control-Request-Method",
        "Access-Control-Request-Headers",
    ],
    expose_headers=["*"],
)
print("CORS middleware configured with explicit settings for development.")


# --- API Endpoints ---


@app.post("/start_task", status_code=201)
async def start_task(request: StartRequest):
    """Initiates task, scans system, initializes AI."""
    task_id = task_manager.create_task(request.initial_prompt, request.task_metadata)
    print(f"INFO: Received start_task. Task ID: {task_id}")

    try:
        await task_manager.initialize_task(
            task_id,
            request.initial_prompt,
            stream_processor.ai_client,
            stream_processor.system_scanner
        )
        print(f"INFO: Task {task_id} initialized successfully.")
        return {"task_id": task_id}
    except Exception as e:
        print(f"CRITICAL ERROR during task start/scan: {type(e).__name__}: {e}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed start task/scan: {e}")


@app.post("/send_message")
async def send_message(request: MessageRequest):
    """Sends user message, streams back responses via SSE."""
    if not task_manager.task_exists(request.task_id):
        print(f"ERROR: Task ID '{request.task_id}' not found.")
        raise HTTPException(status_code=404, detail="Task not found.")

    print(f"INFO: Received message for Task '{request.task_id}': '{request.message[:100]}...'")
    return EventSourceResponse(stream_processor.process_stream(request.task_id, request.message))


@app.get("/task/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """Gets current task status and history."""
    print(f"DEBUG: Status request for Task ID '{task_id}'")
    if not task_manager.task_exists(task_id):
        raise HTTPException(status_code=404, detail="Task not found")

    return TaskStatusResponse(**task_manager.get_task_status_dict(task_id))


@app.post("/task/{task_id}/orchestrator/{mode}")
async def toggle_orchestrator(task_id: str, mode: str):
    """Toggle orchestrator mode for a task (for testing)."""
    if not task_manager.task_exists(task_id):
        raise HTTPException(status_code=404, detail="Task not found")

    use_orchestrator = mode.lower() == "enable"
    task_manager.set_orchestrator_mode(task_id, use_orchestrator)

    return {
        "task_id": task_id,
        "orchestrator_enabled": use_orchestrator,
        "message": f"Orchestrator {'enabled' if use_orchestrator else 'disabled'} for task {task_id}"
    }


# Task recovery and debugging endpoints

@app.post("/task/{task_id}/reset")
async def reset_task(task_id: str):
    """Reset a stuck task to AWAITING_COMMAND state."""
    if not task_manager.task_exists(task_id):
        raise HTTPException(status_code=404, detail="Task not found")

    success = task_manager.reset_stuck_task(task_id)
    if success:
        return {
            "task_id": task_id,
            "message": "Task reset successfully",
            "new_status": "AWAITING_COMMAND"
        }
    else:
        raise HTTPException(status_code=500, detail="Failed to reset task")


@app.post("/task/{task_id}/force-status/{new_status}")
async def force_task_status(task_id: str, new_status: str):
    """Force update task status (for debugging/recovery)."""
    if not task_manager.task_exists(task_id):
        raise HTTPException(status_code=404, detail="Task not found")

    # Validate status
    valid_statuses = [
        "AWAITING_COMMAND", "AWAITING_USER_CONFIRMATION", "AWAITING_USER_INPUT",
        "PROCESSING_COMMAND", "PLANNING", "EXECUTING", "REFINING",
        "COMPLETED", "FAILED", "ABORTED"
    ]

    if new_status not in valid_statuses:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid status. Valid statuses: {', '.join(valid_statuses)}"
        )

    success = task_manager.force_task_status(task_id, new_status)
    if success:
        return {
            "task_id": task_id,
            "message": f"Task status forced to '{new_status}'",
            "new_status": new_status
        }
    else:
        raise HTTPException(status_code=500, detail="Failed to update task status")


@app.post("/tasks/recover-stuck")
async def recover_stuck_tasks():
    """Automatically recover all stuck tasks."""
    recovered_count = task_manager.auto_recover_stuck_tasks()
    stuck_tasks = task_manager.get_stuck_tasks()

    return {
        "message": f"Recovery completed. {recovered_count} tasks recovered.",
        "recovered_count": recovered_count,
        "remaining_stuck_tasks": stuck_tasks
    }


@app.get("/tasks/debug")
async def debug_all_tasks():
    """Get debug information for all tasks."""
    task_statuses = task_manager.get_all_task_statuses()
    stuck_tasks = task_manager.get_stuck_tasks()

    return {
        "total_tasks": len(task_statuses),
        "stuck_tasks": stuck_tasks,
        "task_details": task_statuses
    }


@app.delete("/task/{task_id}")
async def delete_task(task_id: str):
    """Delete a specific task."""
    if not task_manager.task_exists(task_id):
        raise HTTPException(status_code=404, detail="Task not found")

    task_manager.delete_task(task_id)
    return {
        "task_id": task_id,
        "message": "Task deleted successfully"
    }


@app.get("/")
async def root():
    """Basic health check endpoint."""
    print("INFO: Health check '/' accessed.")
    return {
        "message": f"{config.APP_TITLE} is running.",
        "orchestrator_enabled": config.USE_ORCHESTRATOR,
        "version": config.APP_VERSION
    }


@app.options("/{path:path}")
async def options_handler(path: str):
    """Handle OPTIONS requests for CORS preflight."""
    print(f"INFO: OPTIONS request for path: /{path}")
    return {"message": "OK"}


# --- Run with Uvicorn ---
if __name__ == "__main__":
    print("Starting modular FastAPI server using Uvicorn (reload enabled)...")
    import uvicorn
    uvicorn.run("main:app", host=config.SERVER_HOST, port=config.SERVER_PORT, reload=True)