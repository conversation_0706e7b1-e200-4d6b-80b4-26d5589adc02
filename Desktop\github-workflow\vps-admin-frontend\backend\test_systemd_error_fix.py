#!/usr/bin/env python3
"""
Test script to verify that systemd errors are now properly caught by error recovery.
This test simulates the exact error that was causing the infinite loop.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models import SSHResult, TaskEntry, TaskStep, StepStatus
from orchestrator import Orchestrator
from agents import ErrorRecoveryPlanner
from ai_client import AIClient
from ssh_client import SSHClient


async def test_systemd_error_detection():
    """Test that systemd errors are properly detected for error recovery."""
    print("=== Testing Systemd Error Detection ===")
    
    # Create orchestrator instance
    ai_client = AIClient()
    ssh_client = SSHClient()
    orchestrator = Orchestrator(ai_client, ssh_client)
    
    # Create the exact SSH result that was causing the infinite loop
    systemd_ssh_result = SSHResult(
        stdout="",
        stderr="System has not been booted with systemd as init system (PID 1). Can't operate.\nFailed to connect to bus: Host is down",
        exit_status=1,
        success=False,
        command="sudo systemctl reload apache2"
    )
    
    # Test 1: Check if error recovery should be triggered
    should_recover = orchestrator._should_trigger_error_recovery(systemd_ssh_result)
    print(f"✓ Should trigger recovery for systemd error: {should_recover}")
    
    if should_recover:
        print("✅ SUCCESS: Systemd error is now properly detected for recovery!")
    else:
        print("❌ FAILED: Systemd error is still not being detected")
        return False
    
    # Test 2: Test the error recovery planner with systemd error
    print("\n=== Testing Error Recovery Planning ===")
    
    recovery_planner = ErrorRecoveryPlanner(ai_client)
    
    error_details = {
        "stdout": systemd_ssh_result.stdout,
        "stderr": systemd_ssh_result.stderr,
        "exit_status": systemd_ssh_result.exit_status
    }
    
    try:
        recovery_response = await recovery_planner.process(
            failed_command="sudo systemctl reload apache2",
            error_details=error_details,
            original_objective="Reload Apache web server configuration"
        )
        
        print(f"✓ Recovery planning success: {recovery_response.success}")
        print(f"✓ Recovery approach: {recovery_response.recovery_approach}")
        print(f"✓ Recovery steps: {len(recovery_response.recovery_plan)}")
        
        if recovery_response.recovery_approach == "service_management_alternative":
            print("✅ SUCCESS: Recovery planner correctly identified service management alternative!")
        else:
            print(f"⚠️  WARNING: Expected 'service_management_alternative', got '{recovery_response.recovery_approach}'")
        
        # Display the recovery plan
        print("\nRecovery Plan:")
        for i, step in enumerate(recovery_response.recovery_plan, 1):
            print(f"  Step {i}: {step['description']}")
            print(f"    Command: {step['command']}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR in recovery planning: {e}")
        return False


async def test_other_systemd_patterns():
    """Test other systemd-related error patterns."""
    print("\n=== Testing Other Systemd Error Patterns ===")
    
    ai_client = AIClient()
    ssh_client = SSHClient()
    orchestrator = Orchestrator(ai_client, ssh_client)
    
    test_cases = [
        {
            "name": "systemctl command not found",
            "stderr": "bash: systemctl: command not found",
            "command": "systemctl status apache2"
        },
        {
            "name": "systemd is not running",
            "stderr": "Failed to connect to systemd: systemd is not running",
            "command": "systemctl restart nginx"
        },
        {
            "name": "init system error",
            "stderr": "This system is not using systemd as init system",
            "command": "systemctl enable mysql"
        }
    ]
    
    all_passed = True
    
    for test_case in test_cases:
        ssh_result = SSHResult(
            stdout="",
            stderr=test_case["stderr"],
            exit_status=1,
            success=False,
            command=test_case["command"]
        )
        
        should_recover = orchestrator._should_trigger_error_recovery(ssh_result)
        print(f"✓ {test_case['name']}: {'✅ DETECTED' if should_recover else '❌ MISSED'}")
        
        if not should_recover:
            all_passed = False
    
    return all_passed


async def main():
    """Run all systemd error recovery tests."""
    print("Starting Systemd Error Recovery Fix Tests...")
    print("=" * 50)
    
    try:
        # Test the main systemd error that was causing the loop
        test1_passed = await test_systemd_error_detection()
        
        # Test other systemd error patterns
        test2_passed = await test_other_systemd_patterns()
        
        # Summary
        print("\n" + "=" * 50)
        print("SYSTEMD ERROR FIX TEST SUMMARY")
        print("=" * 50)
        print(f"Main Systemd Error Detection: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
        print(f"Other Systemd Patterns: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
        
        if test1_passed and test2_passed:
            print("\n🎉 All systemd error recovery tests PASSED!")
            print("The infinite loop issue should now be FIXED!")
            return True
        else:
            print("\n💥 Some systemd error recovery tests FAILED!")
            return False
            
    except Exception as e:
        print(f"CRITICAL ERROR during testing: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
