/**
 * Message rendering utilities for VPS Admin Chat
 */

import React from 'react';
import {
  <PERSON>ertTriangle,
  CheckCircle,
  XCircle,
  Info,
  HelpCircle,
  Sparkles,
  ChevronDown
} from 'lucide-react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { SSHInfo } from '../types';

/**
 * Create message content based on message type and data
 */
export const createMessageContent = {
  error: (content: string) => (
    React.createElement(React.Fragment, null,
      React.createElement(AlertTriangle, { size: 14, className: "inline mr-1 text-red-500" }),
      content
    )
  ),

  question: (content: string) => (
    React.createElement(React.Fragment, null,
      React.createElement(HelpCircle, { size: 14, className: "inline mr-1 text-purple-500" }),
      content
    )
  ),

  commandRequest: (content: string) => (
    React.createElement('div', { className: 'w-full' },
      React.createElement('p', { className: "mb-2 font-medium" }, "🤖 AI proposes the following command:"),
      React.createElement(SyntaxHighlighter, {
        language: "bash",
        style: vscDarkPlus,
        className: "rounded text-sm w-full code-block-wrapper",
        children: content
      })
    )
  ),

  taskEnd: (content: string) => (
    React.createElement(React.Fragment, null,
      React.createElement(CheckCircle, { size: 14, className: "inline mr-1 text-green-500" }),
      content
    )
  ),

  info: (content: string) => (
    React.createElement(React.Fragment, null,
      React.createElement(Info, { size: 14, className: "inline mr-1 text-blue-500" }),
      content
    )
  ),

  warning: (content: string) => (
    React.createElement(React.Fragment, null,
      React.createElement(AlertTriangle, { size: 14, className: "inline mr-1 text-yellow-500" }),
      content
    )
  ),

  sshOutput: (sshInfo: SSHInfo) => (
    React.createElement('div', { className: "text-xs w-full" },
      React.createElement('p', {
        className: `font-medium mb-1 ${sshInfo?.success ? 'text-green-600' : 'text-red-600'}`
      },
        sshInfo?.success
          ? React.createElement(CheckCircle, { size: 12, className: "inline mr-1" })
          : React.createElement(XCircle, { size: 12, className: "inline mr-1" }),
        `Command ${sshInfo?.success ? 'Succeeded' : 'Failed'} (Exit Code: ${sshInfo?.exit_status ?? 'N/A'})`
      ),
      sshInfo?.stdout && React.createElement('details', { className: "mb-1" },
        React.createElement('summary', { className: "cursor-pointer text-gray-600 hover:text-black" },
          "Stdout ",
          React.createElement(ChevronDown, { size: 12, className: "inline" })
        ),
        React.createElement('pre', {
          className: "mt-1 p-2 bg-gray-800 text-gray-200 rounded overflow-x-auto max-h-40"
        }, sshInfo.stdout)
      ),
      sshInfo?.stderr && React.createElement('details', { open: true, className: "mb-1" },
        React.createElement('summary', { className: "cursor-pointer text-red-600 hover:text-red-800" },
          "Stderr ",
          React.createElement(ChevronDown, { size: 12, className: "inline" })
        ),
        React.createElement('pre', {
          className: "mt-1 p-2 bg-red-900 text-red-100 rounded overflow-x-auto max-h-40"
        }, sshInfo.stderr)
      )
    )
  ),

  summary: (content: string) => (
    React.createElement(React.Fragment, null,
      React.createElement(Sparkles, { size: 14, className: "inline mr-1 text-indigo-500" }),
      content
    )
  )
};

/**
 * Get placeholder text based on UI state
 */
export const getInputPlaceholder = (
  isTaskActive: boolean,
  isWaiting: boolean,
  showConfirmation: boolean,
  isAwaitingAnswer: boolean
): string => {
  if (!isTaskActive) return "Enter initial task to start...";
  if (isWaiting) return "Waiting for AI response...";
  if (showConfirmation) return "Confirm using buttons above";
  if (isAwaitingAnswer) return "Type your answer to the question...";
  return "Type your message or next request...";
};
