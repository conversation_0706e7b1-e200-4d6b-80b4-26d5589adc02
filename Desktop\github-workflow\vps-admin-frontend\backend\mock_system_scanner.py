"""
Mock system scanner for testing purposes.
Provides fake system information without requiring SSH connection.
"""

from typing import Dict, Any


class MockSystemScanner:
    """Mock system scanner that returns fake system information."""
    
    def __init__(self, ssh_client=None):
        # Ignore ssh_client for mock
        pass
    
    async def perform_initial_scan(self) -> str:
        """
        Returns mock system information for testing.
        """
        print("INFO: Performing MOCK system scan (no SSH connection)...")
        
        mock_system_info = """
=== MOCK SYSTEM SCAN RESULTS ===

OS Information:
Ubuntu 20.04.6 LTS (Focal Fossa)
Kernel: Linux 5.4.0-150-generic
Architecture: x86_64

Running Services (limited):
● apache2.service - The Apache HTTP Server
   Loaded: loaded (/lib/systemd/system/apache2.service; enabled; vendor preset: enabled)
   Active: active (running) since Mon 2024-01-15 10:30:00 UTC; 2h 15min ago
● ssh.service - OpenBSD Secure Shell server
   Loaded: loaded (/lib/systemd/system/ssh.service; enabled; vendor preset: enabled)
   Active: active (running) since Mon 2024-01-15 10:30:00 UTC; 2h 15min ago
● systemd-resolved.service - Network Name Resolution
   Loaded: loaded (/lib/systemd/system/systemd-resolved.service; enabled; vendor preset: enabled)
   Active: active (running) since Mon 2024-01-15 10:30:00 UTC; 2h 15min ago

Installed Packages (dpkg, limited):
ii  apache2                    2.4.41-4ubuntu3.14
ii  apache2-bin                2.4.41-4ubuntu3.14
ii  apache2-data               2.4.41-4ubuntu3.14
ii  apache2-utils              2.4.41-4ubuntu3.14
ii  git                        1:2.25.1-1ubuntu3.11
ii  curl                       7.68.0-1ubuntu2.19
ii  wget                       1.20.3-1ubuntu2
ii  nano                       4.8-1ubuntu1
ii  vim                        2:8.1.2269-1ubuntu5.15
ii  openssh-server             1:8.2p1-4ubuntu0.9
ii  ufw                        0.36-6ubuntu1
ii  fail2ban                   0.11.1-1ubuntu1

=== END MOCK SYSTEM SCAN ===
"""
        
        print("INFO: Mock system scan completed successfully.")
        return mock_system_info.strip()
