#!/usr/bin/env python3
"""
Test script for enhanced error handling logic in the VPS Admin system.
Tests the RefinerAgent with stdout, stderr, and alternative approach functionality.
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from agents import RefinerAgent
from models import SSHResult, StepAttempt


async def test_enhanced_refiner_with_stdout_stderr():
    """Test the enhanced RefinerAgent with stdout and stderr information."""
    print("=== Testing Enhanced RefinerAgent with stdout/stderr ===")
    
    config = Config()
    refiner = RefinerAgent(config)
    
    # Simulate failed attempts with detailed stdout/stderr information
    failed_attempts = [
        {
            "command": "apt install docker",
            "error_message": "E: Unable to locate package docker",
            "stdout": "Reading package lists... Done\nBuilding dependency tree... Done",
            "stderr": "E: Unable to locate package docker\nE: Couldn't find any package by glob 'docker'",
            "exit_status": 100
        },
        {
            "command": "sudo apt install docker",
            "error_message": "E: Unable to locate package docker",
            "stdout": "Reading package lists... Done\nBuilding dependency tree... Done",
            "stderr": "E: Unable to locate package docker\nE: Couldn't find any package by glob 'docker'",
            "exit_status": 100
        }
    ]
    
    try:
        response = await refiner.process(
            step_description="Install Docker on Ubuntu system",
            failed_attempts=failed_attempts
        )
        
        print(f"Refiner Success: {response.success}")
        print(f"Original Command: {response.original_command}")
        print(f"Corrected Command: {response.corrected_command}")
        print(f"Analysis: {response.analysis}")
        
        # Check if the corrected command suggests an alternative approach
        corrected_cmd = response.corrected_command.lower()
        alternative_approaches = [
            "docker.io",  # Correct package name
            "docker-ce",  # Docker CE package
            "snap install docker",  # Snap package manager
            "curl",  # Download script approach
            "wget"   # Download script approach
        ]
        
        has_alternative = any(approach in corrected_cmd for approach in alternative_approaches)
        print(f"Suggests Alternative Approach: {has_alternative}")
        
        return response.success and has_alternative
        
    except Exception as e:
        print(f"Enhanced Refiner Test Failed: {e}")
        return False


async def test_permission_error_handling():
    """Test handling of permission-related errors."""
    print("\n=== Testing Permission Error Handling ===")
    
    config = Config()
    refiner = RefinerAgent(config)
    
    # Simulate permission denied error
    failed_attempts = [
        {
            "command": "systemctl start nginx",
            "error_message": "Failed to start nginx.service: Access denied",
            "stdout": "",
            "stderr": "==== AUTHENTICATING FOR org.freedesktop.systemd1.manage-units ===\nAuthentication required to manage system services or other units.\nPolkit: Access denied",
            "exit_status": 1
        }
    ]
    
    try:
        response = await refiner.process(
            step_description="Start nginx service",
            failed_attempts=failed_attempts
        )
        
        print(f"Permission Error Handling Success: {response.success}")
        print(f"Corrected Command: {response.corrected_command}")
        
        # Check if sudo is suggested
        suggests_sudo = "sudo" in response.corrected_command.lower()
        print(f"Suggests sudo: {suggests_sudo}")
        
        return response.success and suggests_sudo
        
    except Exception as e:
        print(f"Permission Error Test Failed: {e}")
        return False


async def test_service_not_found_handling():
    """Test handling of service not found errors."""
    print("\n=== Testing Service Not Found Error Handling ===")
    
    config = Config()
    refiner = RefinerAgent(config)
    
    # Simulate service not found error
    failed_attempts = [
        {
            "command": "systemctl start nonexistent-service",
            "error_message": "Failed to start nonexistent-service.service: Unit nonexistent-service.service not found.",
            "stdout": "",
            "stderr": "Failed to start nonexistent-service.service: Unit nonexistent-service.service not found.",
            "exit_status": 5
        }
    ]
    
    try:
        response = await refiner.process(
            step_description="Start a web service",
            failed_attempts=failed_attempts
        )
        
        print(f"Service Not Found Handling Success: {response.success}")
        print(f"Corrected Command: {response.corrected_command}")
        
        # Check if alternative services are suggested
        corrected_cmd = response.corrected_command.lower()
        alternative_services = ["nginx", "apache2", "httpd", "install", "apt"]
        suggests_alternative = any(service in corrected_cmd for service in alternative_services)
        print(f"Suggests Alternative Service: {suggests_alternative}")
        
        return response.success and suggests_alternative
        
    except Exception as e:
        print(f"Service Not Found Test Failed: {e}")
        return False


async def test_step_attempt_model():
    """Test the enhanced StepAttempt model with new fields."""
    print("\n=== Testing Enhanced StepAttempt Model ===")
    
    try:
        # Create a StepAttempt with the new fields
        ssh_result = SSHResult(
            stdout="Some output",
            stderr="Some error",
            exit_status=1,
            success=False,
            command="test command"
        )
        
        step_attempt = StepAttempt(
            attempt_number=1,
            command="test command",
            ssh_result=ssh_result,
            error_message="Test error",
            stdout="Some output",
            stderr="Some error",
            exit_status=1,
            timestamp=1234567890.0,
            duration_ms=1000
        )
        
        print(f"StepAttempt created successfully")
        print(f"Command: {step_attempt.command}")
        print(f"Stdout: {step_attempt.stdout}")
        print(f"Stderr: {step_attempt.stderr}")
        print(f"Exit Status: {step_attempt.exit_status}")
        
        return True
        
    except Exception as e:
        print(f"StepAttempt Model Test Failed: {e}")
        return False


async def main():
    """Run all enhanced error handling tests."""
    print("Enhanced Error Handling Tests")
    print("=" * 50)
    
    # Check if we have the required environment variables
    if not os.getenv("GEMINI_MODEL"):
        print("ERROR: GEMINI_MODEL environment variable not set")
        print("Please set your Gemini API key to run these tests")
        return
    
    tests = [
        ("Enhanced RefinerAgent", test_enhanced_refiner_with_stdout_stderr),
        ("Permission Error Handling", test_permission_error_handling),
        ("Service Not Found Handling", test_service_not_found_handling),
        ("StepAttempt Model", test_step_attempt_model),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"Test {test_name} crashed: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 50)
    print("ENHANCED ERROR HANDLING TEST RESULTS")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:30} {status}")
        if not result:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 All enhanced error handling tests PASSED!")
        print("The system now provides:")
        print("- Detailed stdout/stderr analysis")
        print("- Alternative approach suggestions")
        print("- Better permission error handling")
        print("- Service not found error handling")
    else:
        print("❌ Some tests FAILED. Please check the implementation.")
    
    return all_passed


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Test runner crashed: {e}")
        sys.exit(1)
