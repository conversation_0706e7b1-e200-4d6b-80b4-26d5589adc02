#!/usr/bin/env python3
"""
Quick utility script to fix stuck tasks in VPS Admin system.
Run this when you get "Task is currently busy" errors.
"""

import requests
import json
import sys
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"

def check_server_status() -> bool:
    """Check if the backend server is running."""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False

def get_debug_info() -> Dict[str, Any]:
    """Get debug information about all tasks."""
    try:
        response = requests.get(f"{BASE_URL}/tasks/debug")
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Failed to get debug info: {response.status_code}")
            return {}
    except requests.exceptions.RequestException as e:
        print(f"❌ Error getting debug info: {e}")
        return {}

def recover_stuck_tasks() -> bool:
    """Automatically recover all stuck tasks."""
    try:
        response = requests.post(f"{BASE_URL}/tasks/recover-stuck")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {data['message']}")
            return True
        else:
            print(f"❌ Failed to recover tasks: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Error recovering tasks: {e}")
        return False

def reset_specific_task(task_id: str) -> bool:
    """Reset a specific task."""
    try:
        response = requests.post(f"{BASE_URL}/task/{task_id}/reset")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {data['message']}")
            return True
        else:
            print(f"❌ Failed to reset task {task_id}: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Error resetting task {task_id}: {e}")
        return False

def delete_task(task_id: str) -> bool:
    """Delete a specific task."""
    try:
        response = requests.delete(f"{BASE_URL}/task/{task_id}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {data['message']}")
            return True
        else:
            print(f"❌ Failed to delete task {task_id}: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Error deleting task {task_id}: {e}")
        return False

def main():
    """Main function to handle stuck tasks."""
    print("🔧 VPS Admin Task Recovery Utility")
    print("=" * 40)
    
    # Check if server is running
    if not check_server_status():
        print("❌ Backend server is not running or not accessible.")
        print("   Please make sure the backend is running on http://localhost:8000")
        sys.exit(1)
    
    print("✅ Backend server is accessible")
    
    # Get debug information
    print("\n📊 Getting task status information...")
    debug_info = get_debug_info()
    
    if not debug_info:
        print("❌ Could not retrieve task information")
        sys.exit(1)
    
    total_tasks = debug_info.get('total_tasks', 0)
    stuck_tasks = debug_info.get('stuck_tasks', [])
    task_details = debug_info.get('task_details', {})
    
    print(f"📈 Total tasks: {total_tasks}")
    print(f"🚫 Stuck tasks: {len(stuck_tasks)}")
    
    if stuck_tasks:
        print("\n🚫 Stuck task details:")
        for task_id in stuck_tasks:
            if task_id in task_details:
                task = task_details[task_id]
                print(f"  - {task_id[:8]}... | Status: {task['status']} | Age: {task['age_minutes']}min | Title: {task['title']}")
    
    # Show all tasks if requested
    if len(sys.argv) > 1 and sys.argv[1] == "--show-all":
        print(f"\n📋 All task details:")
        for task_id, task in task_details.items():
            status_emoji = "🚫" if task_id in stuck_tasks else "✅"
            print(f"  {status_emoji} {task_id[:8]}... | {task['status']} | {task['age_minutes']}min | {task['title']}")
    
    # Handle recovery
    if stuck_tasks:
        print(f"\n🔧 Found {len(stuck_tasks)} stuck tasks. Attempting recovery...")
        if recover_stuck_tasks():
            print("✅ Recovery completed successfully!")
        else:
            print("❌ Automatic recovery failed. You may need to restart the backend.")
    else:
        print("\n✅ No stuck tasks found. System appears healthy!")
    
    # Final status check
    print("\n🔍 Final status check...")
    final_debug = get_debug_info()
    final_stuck = final_debug.get('stuck_tasks', [])
    
    if final_stuck:
        print(f"⚠️  Still have {len(final_stuck)} stuck tasks after recovery.")
        print("   You may need to restart the backend server.")
    else:
        print("✅ All tasks are now in healthy states!")

if __name__ == "__main__":
    main()
