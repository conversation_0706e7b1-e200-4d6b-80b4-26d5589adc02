/**
 * ConfirmationButtons component for command confirmation
 */

import React, { useState } from 'react';
import { CheckC<PERSON><PERSON>, XCircle, SkipForward, RotateCcw, Square, ChevronDown, ChevronUp } from 'lucide-react';
import { ConfirmationButtonsProps } from '../types';

const ConfirmationButtons: React.FC<ConfirmationButtonsProps> = ({
  onConfirm,
  isVisible,
  options = [],
  showAdvancedOptions = false
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  if (!isVisible) return null;

  const hasAdvancedOptions = showAdvancedOptions || options.includes('skip_step') || options.includes('retry_step') || options.includes('abort_task');

  return (
    <div className="mt-3 pt-2 border-t border-cyan-700/50">
      {/* Primary confirmation buttons */}
      <div className="flex justify-end items-center gap-2">
        <p className="text-xs font-medium text-white/80 mr-auto">Confirm execution?</p>

        {hasAdvancedOptions && (
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="px-2 py-1 text-xs font-medium bg-gray-600 text-white rounded-full hover:bg-gray-700 transition-colors duration-150 flex items-center gap-1 active:scale-95 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1 focus:ring-offset-teal-600"
          >
            {showAdvanced ? <ChevronUp size={12} /> : <ChevronDown size={12} />}
            Options
          </button>
        )}

        <button
          onClick={() => onConfirm('yes')}
          className="px-2.5 py-1 text-xs font-medium bg-green-600 text-white rounded-full hover:bg-green-700 transition-colors duration-150 flex items-center gap-1 active:scale-95 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1 focus:ring-offset-teal-600"
        >
          <CheckCircle size={12} /> Yes
        </button>
        <button
          onClick={() => onConfirm('no')}
          className="px-2.5 py-1 text-xs font-medium bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors duration-150 flex items-center gap-1 active:scale-95 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 focus:ring-offset-teal-600"
        >
          <XCircle size={12} /> No
        </button>
      </div>

      {/* Advanced options */}
      {showAdvanced && hasAdvancedOptions && (
        <div className="mt-2 pt-2 border-t border-cyan-700/30 flex justify-end items-center gap-2">
          <p className="text-xs font-medium text-white/60 mr-auto">Advanced options:</p>

          {(options.includes('skip_step') || showAdvancedOptions) && (
            <button
              onClick={() => onConfirm('skip_step')}
              className="px-2.5 py-1 text-xs font-medium bg-yellow-600 text-white rounded-full hover:bg-yellow-700 transition-colors duration-150 flex items-center gap-1 active:scale-95 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-1 focus:ring-offset-teal-600"
            >
              <SkipForward size={12} /> Skip Step
            </button>
          )}

          {(options.includes('retry_step') || showAdvancedOptions) && (
            <button
              onClick={() => onConfirm('retry_step')}
              className="px-2.5 py-1 text-xs font-medium bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors duration-150 flex items-center gap-1 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 focus:ring-offset-teal-600"
            >
              <RotateCcw size={12} /> Retry Step
            </button>
          )}

          {(options.includes('abort_task') || showAdvancedOptions) && (
            <button
              onClick={() => onConfirm('abort_task')}
              className="px-2.5 py-1 text-xs font-medium bg-red-800 text-white rounded-full hover:bg-red-900 transition-colors duration-150 flex items-center gap-1 active:scale-95 focus:outline-none focus:ring-2 focus:ring-red-700 focus:ring-offset-1 focus:ring-offset-teal-600"
            >
              <Square size={12} /> Abort Task
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default ConfirmationButtons;
