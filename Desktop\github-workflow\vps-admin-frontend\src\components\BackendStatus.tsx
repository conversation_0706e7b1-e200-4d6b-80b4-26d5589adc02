/**
 * Backend Status Component
 * Provides diagnostics and status information for the backend connection
 */

import React, { useState, useEffect } from 'react';
import { AlertTriangle, CheckCircle, XCircle, RefreshCw, Server, Wifi, Clock, HelpCircle } from 'lucide-react';
import { apiService } from '../services/apiService';
import TroubleshootingGuide from './TroubleshootingGuide';

interface BackendStatusProps {
  onStatusChange?: (isOnline: boolean) => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface BackendStatusData {
  isOnline: boolean;
  health: boolean;
  connection: { success: boolean; message: string; latency?: number; details?: any };
  endpoints: { [key: string]: boolean };
  recommendations: string[];
}

export const BackendStatus: React.FC<BackendStatusProps> = ({
  onStatusChange,
  autoRefresh = true,
  refreshInterval = 30000 // 30 seconds
}) => {
  const [status, setStatus] = useState<BackendStatusData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [showTroubleshooting, setShowTroubleshooting] = useState(false);

  const checkStatus = async () => {
    setIsLoading(true);
    try {
      const statusData = await apiService.getBackendStatus();
      setStatus(statusData);
      setLastChecked(new Date());
      onStatusChange?.(statusData.isOnline);
    } catch (error) {
      console.error('Failed to check backend status:', error);
      setStatus({
        isOnline: false,
        health: false,
        connection: { success: false, message: 'Failed to check status' },
        endpoints: {},
        recommendations: ['Unable to perform status check. Please verify your network connection.']
      });
      onStatusChange?.(false);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkStatus();
  }, []);

  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(checkStatus, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const getStatusIcon = () => {
    if (isLoading) {
      return <RefreshCw className="w-5 h-5 animate-spin text-blue-500" />;
    }

    if (!status) {
      return <XCircle className="w-5 h-5 text-gray-400" />;
    }

    if (status.isOnline && status.health) {
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    } else if (status.isOnline) {
      return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
    } else {
      return <XCircle className="w-5 h-5 text-red-500" />;
    }
  };

  const getStatusText = () => {
    if (isLoading) return 'Checking...';
    if (!status) return 'Unknown';
    if (status.isOnline && status.health) return 'Online';
    if (status.isOnline) return 'Issues Detected';
    return 'Offline';
  };

  const getStatusColor = () => {
    if (isLoading) return 'text-blue-600';
    if (!status) return 'text-gray-600';
    if (status.isOnline && status.health) return 'text-green-600';
    if (status.isOnline) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Server className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-800">Backend Status</h3>
        </div>
        <button
          onClick={checkStatus}
          disabled={isLoading}
          className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
          title="Refresh status"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      <div className="space-y-3">
        {/* Overall Status */}
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <span className={`font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
          {status?.connection.latency && (
            <div className="flex items-center space-x-1 text-sm text-gray-500">
              <Clock className="w-4 h-4" />
              <span>{status.connection.latency}ms</span>
            </div>
          )}
        </div>

        {/* Connection Details */}
        {status && (
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <Wifi className="w-4 h-4 text-gray-500" />
              <span className="text-gray-700">Connection:</span>
              <span className={status.connection.success ? 'text-green-600' : 'text-red-600'}>
                {status.connection.message}
              </span>
            </div>

            {/* Endpoint Status */}
            {Object.keys(status.endpoints).length > 0 && (
              <div className="mt-2">
                <div className="text-gray-700 font-medium mb-1">Endpoints:</div>
                <div className="space-y-1">
                  {Object.entries(status.endpoints).map(([endpoint, isWorking]) => (
                    <div key={endpoint} className="flex items-center space-x-2 ml-4">
                      <div className={`w-2 h-2 rounded-full ${isWorking ? 'bg-green-500' : 'bg-red-500'}`} />
                      <span className="text-gray-600">{endpoint}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recommendations */}
            {status.recommendations.length > 0 && (
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                  <span className="text-yellow-800 font-medium">Recommendations:</span>
                </div>
                <ul className="space-y-1 text-yellow-700 text-sm">
                  {status.recommendations.map((rec, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <span className="text-yellow-600 mt-1">•</span>
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Last Checked */}
        {lastChecked && (
          <div className="text-xs text-gray-500 mt-2">
            Last checked: {lastChecked.toLocaleTimeString()}
          </div>
        )}

        {/* Troubleshooting Section */}
        {(!status?.isOnline || status.recommendations.length > 0) && (
          <div className="mt-4 border-t border-gray-200 pt-3">
            <button
              onClick={() => setShowTroubleshooting(!showTroubleshooting)}
              className="flex items-center space-x-2 text-sm text-blue-600 hover:text-blue-800 transition-colors"
            >
              <HelpCircle className="w-4 h-4" />
              <span>{showTroubleshooting ? 'Hide' : 'Show'} Troubleshooting Guide</span>
            </button>

            {showTroubleshooting && (
              <div className="mt-3">
                <TroubleshootingGuide />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BackendStatus;
