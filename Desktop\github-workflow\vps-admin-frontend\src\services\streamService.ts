/**
 * Stream service for handling Server-Sent Events (SSE)
 */

import { SSEEventData } from '../types';
import { API_BASE_URL } from '../constants';

export type StreamEventHandler = (data: SSEEventData) => void;
export type StreamErrorHandler = (error: Error) => void;
export type StreamCloseHandler = () => void;

class StreamService {
  private eventSource: EventSource | null = null;
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Start streaming messages for a task
   */
  startStream(
    taskId: string,
    message: string,
    onMessage: StreamEventHandler,
    onError: StreamErrorHandler,
    onClose: StreamCloseHandler
  ): void {
    // Close existing stream if any
    this.closeStream();

    try {
      // Create URL with query parameters
      const url = new URL(`${this.baseUrl}/send_message`);
      url.searchParams.append('task_id', taskId);
      url.searchParams.append('message', message);

      // Create EventSource
      this.eventSource = new EventSource(url.toString());

      // Set up event listeners
      this.eventSource.onmessage = (event) => {
        try {
          const data: SSEEventData = JSON.parse(event.data);
          onMessage(data);
        } catch (error) {
          console.error('Error parsing SSE data:', error);
          onError(new Error('Failed to parse server response'));
        }
      };

      this.eventSource.onerror = (event) => {
        console.error('SSE error:', event);

        // Check if the connection is closed
        if (this.eventSource?.readyState === EventSource.CLOSED) {
          onClose();
        } else {
          onError(new Error('Stream connection error'));
        }
      };

      this.eventSource.addEventListener('end', () => {
        console.log('Stream ended by server');
        this.closeStream();
        onClose();
      });

      this.eventSource.addEventListener('error', (event: any) => {
        console.error('Stream error event:', event);
        try {
          const data = JSON.parse(event.data);
          onError(new Error(data.content || 'Stream error'));
        } catch {
          onError(new Error('Stream error'));
        }
      });

    } catch (error) {
      console.error('Error starting stream:', error);
      onError(new Error(`Failed to start stream: ${error instanceof Error ? error.message : 'Unknown error'}`));
    }
  }

  /**
   * Close the current stream
   */
  closeStream(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }

  /**
   * Check if stream is active
   */
  isStreamActive(): boolean {
    return this.eventSource !== null && this.eventSource.readyState === EventSource.OPEN;
  }

  /**
   * Get stream state
   */
  getStreamState(): string {
    if (!this.eventSource) return 'CLOSED';

    switch (this.eventSource.readyState) {
      case EventSource.CONNECTING:
        return 'CONNECTING';
      case EventSource.OPEN:
        return 'OPEN';
      case EventSource.CLOSED:
        return 'CLOSED';
      default:
        return 'UNKNOWN';
    }
  }

  /**
   * Update base URL
   */
  setBaseUrl(url: string): void {
    this.baseUrl = url;
  }

  /**
   * Get current base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
}

// Create and export singleton instance
export const streamService = new StreamService();
export default streamService;
