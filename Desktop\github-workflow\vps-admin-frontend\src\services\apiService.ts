/**
 * API service for VPS Admin Chat
 */

import { StartTaskRequest, StartTaskResponse, SendMessageRequest } from '../types';
import { API_BASE_URL } from '../constants';

class ApiService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Start a new task
   */
  async startTask(request: StartTaskRequest): Promise<StartTaskResponse> {
    try {
      // First check if backend is reachable
      const healthCheck = await this.healthCheck();
      if (!healthCheck) {
        throw new Error('Backend server is not responding. Please ensure the backend server is running on port 8000.');
      }

      const response = await fetch(`${this.baseUrl}/start_task`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(request),
        mode: 'cors',
      });

      if (!response.ok) {
        let errorText = '';
        try {
          errorText = await response.text();
        } catch (e) {
          errorText = `HTTP ${response.status} ${response.statusText}`;
        }

        if (response.status === 500) {
          throw new Error(`Backend server error: ${errorText}. Please check the backend server logs.`);
        } else if (response.status === 404) {
          throw new Error(`Endpoint not found: ${errorText}. Please verify the backend server is running the correct version.`);
        } else {
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error starting task:', error);

      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        throw new Error('Cannot connect to backend server. Please ensure the backend is running on http://localhost:8000 and CORS is properly configured.');
      }

      throw new Error(`Failed to start task: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Send a message to an existing task
   */
  async sendMessage(request: SendMessageRequest): Promise<Response> {
    try {
      const response = await fetch(`${this.baseUrl}/send_message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return response;
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error(`Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get task status
   */
  async getTaskStatus(taskId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/task/${taskId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting task status:', error);
      throw new Error(`Failed to get task status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(`${this.baseUrl}/`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        mode: 'cors',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        console.log('Backend health check successful');
        return true;
      } else {
        console.warn(`Backend health check failed with status: ${response.status}`);
        return false;
      }
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.warn('Health check timed out after 5 seconds');
        } else if (error.message.includes('Failed to fetch')) {
          console.warn('Health check failed: Cannot connect to backend server');
        } else {
          console.warn('Health check failed:', error.message);
        }
      } else {
        console.warn('Health check failed with unknown error:', error);
      }
      return false;
    }
  }

  /**
   * Test connection to backend
   */
  async testConnection(): Promise<{ success: boolean; message: string; latency?: number; details?: any }> {
    const startTime = Date.now();

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(`${this.baseUrl}/`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        mode: 'cors',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      const latency = Date.now() - startTime;

      if (response.ok) {
        try {
          const data = await response.json();
          return {
            success: true,
            message: 'Connection successful',
            latency,
            details: data
          };
        } catch (e) {
          return {
            success: true,
            message: 'Connection successful (non-JSON response)',
            latency
          };
        }
      } else {
        let errorDetails = '';
        try {
          errorDetails = await response.text();
        } catch (e) {
          errorDetails = 'Could not read error response';
        }

        return {
          success: false,
          message: `Server responded with status ${response.status}: ${response.statusText}`,
          latency,
          details: errorDetails
        };
      }
    } catch (error) {
      const latency = Date.now() - startTime;

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return {
            success: false,
            message: 'Connection timed out after 10 seconds',
            latency
          };
        } else if (error.message.includes('Failed to fetch')) {
          return {
            success: false,
            message: 'Cannot connect to backend server. Please check if the server is running on http://localhost:8000',
            latency
          };
        } else {
          return {
            success: false,
            message: `Connection failed: ${error.message}`,
            latency
          };
        }
      } else {
        return {
          success: false,
          message: `Connection failed: Unknown error`,
          latency
        };
      }
    }
  }

  /**
   * Get server info
   */
  async getServerInfo(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.warn('Failed to get server info:', error);
      return null;
    }
  }

  /**
   * Update base URL
   */
  setBaseUrl(url: string): void {
    this.baseUrl = url;
  }

  /**
   * Get current base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }

  /**
   * Comprehensive backend status check
   */
  async getBackendStatus(): Promise<{
    isOnline: boolean;
    health: boolean;
    connection: { success: boolean; message: string; latency?: number; details?: any };
    endpoints: { [key: string]: boolean };
    recommendations: string[];
  }> {
    const status = {
      isOnline: false,
      health: false,
      connection: await this.testConnection(),
      endpoints: {} as { [key: string]: boolean },
      recommendations: [] as string[]
    };
    status.isOnline = status.connection.success;

    // Test health endpoint
    if (status.isOnline) {
      status.health = await this.healthCheck();
    }

    // Test specific endpoints
    const endpointsToTest = ['/start_task', '/send_message'];
    for (const endpoint of endpointsToTest) {
      try {
        const response = await fetch(`${this.baseUrl}${endpoint}`, {
          method: 'OPTIONS', // Use OPTIONS to test CORS without triggering the actual endpoint
          headers: {
            'Accept': 'application/json',
          },
          mode: 'cors',
        });
        status.endpoints[endpoint] = response.ok || response.status === 405; // 405 Method Not Allowed is acceptable for OPTIONS
      } catch (error) {
        status.endpoints[endpoint] = false;
      }
    }

    // Generate recommendations
    if (!status.isOnline) {
      status.recommendations.push('Backend server is not running. Please start the backend server on port 8000.');
      status.recommendations.push('Navigate to the backend directory and run: python main.py');
    } else if (!status.health) {
      status.recommendations.push('Backend server is running but health check failed. Check server logs for errors.');
      status.recommendations.push('Verify all required environment variables are set in the .env file.');
    } else {
      const failedEndpoints = Object.entries(status.endpoints)
        .filter(([_, success]) => !success)
        .map(([endpoint, _]) => endpoint);

      if (failedEndpoints.length > 0) {
        status.recommendations.push(`Some endpoints are not responding: ${failedEndpoints.join(', ')}`);
        status.recommendations.push('Check if the backend server is running the correct version.');
      }
    }

    if (status.connection.latency && status.connection.latency > 5000) {
      status.recommendations.push('High latency detected. Check network connection or server performance.');
    }

    return status;
  }
}

// Create and export singleton instance
export const apiService = new ApiService();
export default apiService;
