#!/usr/bin/env python3
"""
Test script for the enhanced error recovery system.
Tests the ErrorRecoveryPlanner and recursive error handling.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from agents import ErrorRecoveryPlanner
from models import SSHResult


async def test_error_recovery_planner():
    """Test the ErrorRecoveryPlanner with various failure scenarios."""
    print("\n=== Testing ErrorRecoveryPlanner ===")
    
    config = Config()
    recovery_planner = ErrorRecoveryPlanner(config)
    
    # Test Case 1: Directory already exists error
    print("\n--- Test Case 1: Directory Already Exists ---")
    failed_command = "git clone https://github.com/user/portfolio /var/www/html/portfolio"
    error_details = {
        "stdout": "",
        "stderr": "fatal: destination path '/var/www/html/portfolio' already exists and is not an empty directory.",
        "exit_status": 128
    }
    original_objective = "Clone portfolio repository to web directory"
    
    try:
        response = await recovery_planner.process(
            failed_command=failed_command,
            error_details=error_details,
            original_objective=original_objective
        )
        
        print(f"Recovery Success: {response.success}")
        print(f"Recovery Approach: {response.recovery_approach}")
        print(f"Recovery Steps: {len(response.recovery_plan)}")
        
        for i, step in enumerate(response.recovery_plan, 1):
            print(f"  Step {i}: {step['description']}")
            print(f"    Command: {step['command']}")
        
        assert response.success, "Recovery planning should succeed"
        assert len(response.recovery_plan) > 0, "Should have recovery steps"
        assert response.recovery_approach == "cleanup_and_retry", "Should detect cleanup approach"
        
        print("✅ Test Case 1 PASSED")
        
    except Exception as e:
        print(f"❌ Test Case 1 FAILED: {e}")
        return False
    
    # Test Case 2: Permission denied error
    print("\n--- Test Case 2: Permission Denied ---")
    failed_command = "mkdir /var/www/html/app"
    error_details = {
        "stdout": "",
        "stderr": "mkdir: cannot create directory '/var/www/html/app': Permission denied",
        "exit_status": 1
    }
    original_objective = "Create application directory"
    
    try:
        response = await recovery_planner.process(
            failed_command=failed_command,
            error_details=error_details,
            original_objective=original_objective
        )
        
        print(f"Recovery Success: {response.success}")
        print(f"Recovery Approach: {response.recovery_approach}")
        print(f"Recovery Steps: {len(response.recovery_plan)}")
        
        for i, step in enumerate(response.recovery_plan, 1):
            print(f"  Step {i}: {step['description']}")
            print(f"    Command: {step['command']}")
        
        assert response.success, "Recovery planning should succeed"
        assert len(response.recovery_plan) > 0, "Should have recovery steps"
        assert response.recovery_approach == "fix_permissions", "Should detect permission approach"
        
        print("✅ Test Case 2 PASSED")
        
    except Exception as e:
        print(f"❌ Test Case 2 FAILED: {e}")
        return False
    
    # Test Case 3: Package not found error
    print("\n--- Test Case 3: Package Not Found ---")
    failed_command = "apt install docker"
    error_details = {
        "stdout": "Reading package lists...\nBuilding dependency tree...",
        "stderr": "E: Unable to locate package docker",
        "exit_status": 100
    }
    original_objective = "Install Docker"
    
    try:
        response = await recovery_planner.process(
            failed_command=failed_command,
            error_details=error_details,
            original_objective=original_objective
        )
        
        print(f"Recovery Success: {response.success}")
        print(f"Recovery Approach: {response.recovery_approach}")
        print(f"Recovery Steps: {len(response.recovery_plan)}")
        
        for i, step in enumerate(response.recovery_plan, 1):
            print(f"  Step {i}: {step['description']}")
            print(f"    Command: {step['command']}")
        
        assert response.success, "Recovery planning should succeed"
        assert len(response.recovery_plan) > 0, "Should have recovery steps"
        assert response.recovery_approach == "install_dependencies", "Should detect dependency approach"
        
        print("✅ Test Case 3 PASSED")
        
    except Exception as e:
        print(f"❌ Test Case 3 FAILED: {e}")
        return False
    
    return True


async def test_ssh_result_integration():
    """Test integration with SSHResult objects."""
    print("\n=== Testing SSHResult Integration ===")
    
    config = Config()
    recovery_planner = ErrorRecoveryPlanner(config)
    
    # Create an SSHResult object
    ssh_result = SSHResult(
        stdout="",
        stderr="fatal: destination path '/var/www/html/portfolio' already exists and is not an empty directory.",
        exit_status=128,
        success=False,
        command="git clone https://github.com/user/portfolio /var/www/html/portfolio"
    )
    
    # Convert to error_details format
    error_details = {
        "stdout": ssh_result.stdout,
        "stderr": ssh_result.stderr,
        "exit_status": ssh_result.exit_status
    }
    
    try:
        response = await recovery_planner.process(
            failed_command=ssh_result.command,
            error_details=error_details,
            original_objective="Clone portfolio repository"
        )
        
        print(f"Integration Success: {response.success}")
        print(f"Recovery Steps: {len(response.recovery_plan)}")
        
        assert response.success, "Integration should work"
        assert len(response.recovery_plan) > 0, "Should have recovery steps"
        
        print("✅ SSHResult Integration PASSED")
        return True
        
    except Exception as e:
        print(f"❌ SSHResult Integration FAILED: {e}")
        return False


async def main():
    """Run all error recovery tests."""
    print("Starting Error Recovery System Tests...")
    
    try:
        # Test the ErrorRecoveryPlanner
        test1_passed = await test_error_recovery_planner()
        
        # Test SSHResult integration
        test2_passed = await test_ssh_result_integration()
        
        # Summary
        print("\n" + "="*50)
        print("ERROR RECOVERY TEST SUMMARY")
        print("="*50)
        print(f"ErrorRecoveryPlanner Tests: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
        print(f"SSHResult Integration: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
        
        if test1_passed and test2_passed:
            print("\n🎉 All error recovery tests PASSED!")
            return True
        else:
            print("\n💥 Some error recovery tests FAILED!")
            return False
            
    except Exception as e:
        print(f"\n💥 CRITICAL ERROR in error recovery tests: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
