/**
 * StatsPanel component for displaying statistics and quick actions
 */

import React from 'react';
import { Target, XCircle, Download, Play, Pause } from 'lucide-react';
import { StatsPanelProps } from '../types';

const StatsPanel: React.FC<StatsPanelProps> = ({
  executionStats,
  currentTask,
  taskHistory,
  messages,
  autoScroll,
  onClose,
  onExportHistory,
  onToggleAutoScroll
}) => {
  const getSuccessRate = () => {
    if (executionStats.totalCommands === 0) return 0;
    return Math.round((executionStats.successfulCommands / executionStats.totalCommands) * 100);
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}m ${secs}s`;
  };

  return (
    <div className="bg-white border-0 lg:border border-gray-200 rounded-none lg:rounded-lg shadow-none lg:shadow-xl h-full w-full p-3 lg:p-4 overflow-y-auto">
      <div className="flex items-center justify-between mb-3 lg:mb-4">
        <h3 className="font-semibold text-gray-700 flex items-center gap-2">
          <Target size={16} className="text-blue-500" />
          Statistics
        </h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors p-1 touch-manipulation"
          title="Close Statistics Panel"
        >
          <XCircle size={16} />
        </button>
      </div>

      <div className="space-y-4">
        {/* Execution Stats */}
        <div className="bg-gray-50 p-2 lg:p-3 rounded-lg">
          <h4 className="font-medium text-sm text-gray-600 mb-2">Command Execution</h4>
          <div className="grid grid-cols-2 gap-1.5 lg:gap-2 text-xs">
            <div className="bg-white p-2 rounded">
              <div className="text-gray-500">Total</div>
              <div className="font-bold text-base lg:text-lg">{executionStats.totalCommands}</div>
            </div>
            <div className="bg-white p-2 rounded">
              <div className="text-gray-500">Success Rate</div>
              <div className="font-bold text-base lg:text-lg text-green-600">
                {getSuccessRate()}%
              </div>
            </div>
            <div className="bg-white p-2 rounded">
              <div className="text-gray-500">Successful</div>
              <div className="font-bold text-base lg:text-lg text-green-600">{executionStats.successfulCommands}</div>
            </div>
            <div className="bg-white p-2 rounded">
              <div className="text-gray-500">Failed</div>
              <div className="font-bold text-lg text-red-600">{executionStats.failedCommands}</div>
            </div>
          </div>
          {executionStats.averageExecutionTime > 0 && (
            <div className="mt-2 text-xs text-gray-600">
              Avg. execution time: {executionStats.averageExecutionTime}ms
            </div>
          )}
        </div>

        {/* Current Task */}
        {currentTask && (
          <div className="bg-gray-50 p-3 rounded-lg">
            <h4 className="font-medium text-sm text-gray-600 mb-2">Current Task</h4>
            <div className="text-xs">
              <div className="font-medium truncate">{currentTask.title}</div>
              <div className="text-gray-500 mt-1">
                Status: <span className={`font-medium ${
                  currentTask.status === 'running' ? 'text-blue-600' :
                  currentTask.status === 'completed' ? 'text-green-600' :
                  currentTask.status === 'failed' ? 'text-red-600' :
                  'text-gray-600'
                }`}>
                  {currentTask.status}
                </span>
              </div>
              <div className="text-gray-500 mt-1">Progress: {Math.round(currentTask.progress)}%</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    currentTask.status === 'completed' ? 'bg-green-500' :
                    currentTask.status === 'failed' ? 'bg-red-500' :
                    'bg-teal-500'
                  }`}
                  style={{ width: `${currentTask.progress}%` }}
                />
              </div>
              {currentTask.startTime && (
                <div className="text-gray-500 mt-1">
                  Started: {currentTask.startTime.toLocaleTimeString()}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Task History Summary */}
        {taskHistory.length > 0 && (
          <div className="bg-gray-50 p-3 rounded-lg">
            <h4 className="font-medium text-sm text-gray-600 mb-2">Task History</h4>
            <div className="text-xs">
              <div className="flex justify-between">
                <span>Total tasks:</span>
                <span className="font-mono">{taskHistory.length}</span>
              </div>
              <div className="flex justify-between mt-1">
                <span>Completed:</span>
                <span className="font-mono text-green-600">
                  {taskHistory.filter(t => t.status === 'completed').length}
                </span>
              </div>
              <div className="flex justify-between mt-1">
                <span>Failed:</span>
                <span className="font-mono text-red-600">
                  {taskHistory.filter(t => t.status === 'failed').length}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Session Info */}
        <div className="bg-gray-50 p-3 rounded-lg">
          <h4 className="font-medium text-sm text-gray-600 mb-2">Session Info</h4>
          <div className="text-xs">
            <div className="flex justify-between">
              <span>Uptime:</span>
              <span className="font-mono">{formatUptime(executionStats.uptime)}</span>
            </div>
            <div className="flex justify-between mt-1">
              <span>Messages:</span>
              <span className="font-mono">{messages.length}</span>
            </div>
            <div className="flex justify-between mt-1">
              <span>Auto-scroll:</span>
              <span className={`font-mono ${autoScroll ? 'text-green-600' : 'text-gray-600'}`}>
                {autoScroll ? 'On' : 'Off'}
              </span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-gray-50 p-3 rounded-lg">
          <h4 className="font-medium text-sm text-gray-600 mb-2">Quick Actions</h4>
          <div className="space-y-2">
            <button
              onClick={onExportHistory}
              className="w-full text-xs bg-blue-500 text-white px-3 py-2 rounded hover:bg-blue-600 flex items-center gap-2 transition-colors"
              disabled={taskHistory.length === 0 && messages.length === 0}
            >
              <Download size={12} />
              Export History
            </button>
            <button
              onClick={onToggleAutoScroll}
              className={`w-full text-xs px-3 py-2 rounded flex items-center gap-2 transition-colors ${
                autoScroll
                  ? 'bg-green-500 text-white hover:bg-green-600'
                  : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
              }`}
            >
              {autoScroll ? <Pause size={12} /> : <Play size={12} />}
              Auto Scroll: {autoScroll ? 'On' : 'Off'}
            </button>
          </div>
        </div>

        {/* Performance Metrics */}
        {executionStats.totalCommands > 0 && (
          <div className="bg-gray-50 p-3 rounded-lg">
            <h4 className="font-medium text-sm text-gray-600 mb-2">Performance</h4>
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span>Commands/min:</span>
                <span className="font-mono">
                  {executionStats.uptime > 0
                    ? Math.round((executionStats.totalCommands / executionStats.uptime) * 60)
                    : 0
                  }
                </span>
              </div>
              <div className="flex justify-between">
                <span>Error rate:</span>
                <span className="font-mono text-red-600">
                  {Math.round((executionStats.failedCommands / executionStats.totalCommands) * 100)}%
                </span>
              </div>
            </div>
          </div>
        )}

        {/* System Status */}
        <div className="bg-gray-50 p-3 rounded-lg">
          <h4 className="font-medium text-sm text-gray-600 mb-2">System Status</h4>
          <div className="text-xs space-y-1">
            <div className="flex items-center justify-between">
              <span>Connection:</span>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-green-600">Active</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatsPanel;
