#!/usr/bin/env python3
"""
Test script to verify the orchestrator fix is working.
"""

import asyncio
import json
from config import Config
from models import TaskEntry, TaskPlan, TaskStep, StepStatus
from orchestrator import TaskOrchestrator
from ssh_client import SSHClient

async def test_orchestrator_fix():
    """Test that the orchestrator can progress past planning phase."""
    
    print("=== Testing Orchestrator Fix ===")
    
    # Setup
    config = Config()
    ssh_client = SSHClient(config)
    orchestrator = TaskOrchestrator(config, ssh_client)
    
    # Create a test task
    task = TaskEntry(
        id="test-fix-123",
        title="Test Portfolio Hosting",
        description="Host a static web app",
        priority="medium",
        history=[],
        chat=None,
        status="PENDING",
        command_to_confirm=None,
        system_info="Ubuntu 20.04 LTS, Apache web server available, git installed",
        created_at=asyncio.get_event_loop().time(),
        current_step=0,
        estimated_steps=0,
        commands_executed=0,
        commands_successful=0,
        commands_failed=0,
        recent_commands=[],
        metadata={},
        original_prompt="host this static web app: https://github.com/Moetez-Baklouti/portfolio",
        use_orchestrator=True,
        main_context_history=[],
        current_step_attempts=[],
        task_plan=None,
        final_output="",
        max_retries_per_step=3
    )
    
    user_message = "host this static web app: https://github.com/Moetez-Baklouti/portfolio"
    
    print(f"Initial task status: {task.status}")
    print(f"Task has plan: {task.task_plan is not None}")
    
    # Track status changes
    status_changes = []
    
    try:
        print("\nStarting orchestrator execution...")
        async for event in orchestrator.execute_task(task, user_message):
            event_data = json.loads(event.data)
            print(f"Event: {event_data.get('type')} - {event_data.get('content')}")
            
            # Track status changes
            if task.status not in [s[0] for s in status_changes]:
                status_changes.append((task.status, event_data.get('type')))
            
            # Stop after planning phase to test the fix
            if task.status == "PLANNED":
                print(f"\n✅ SUCCESS: Task progressed to PLANNED status!")
                print(f"Task now has plan: {task.task_plan is not None}")
                if task.task_plan:
                    print(f"Plan has {len(task.task_plan.steps)} steps")
                break
            elif task.status == "EXECUTING":
                print(f"\n🎉 EXCELLENT: Task progressed to EXECUTING status!")
                print("The orchestrator fix is working correctly!")
                break
            elif task.status == "FAILED":
                print(f"\n❌ FAILED: Task failed during execution")
                break
                
    except Exception as e:
        print(f"\n❌ Exception during orchestrator execution: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\nFinal task status: {task.status}")
    print(f"Status progression: {' -> '.join([s[0] for s in status_changes])}")
    
    if task.status in ["PLANNED", "EXECUTING"]:
        print("\n🎉 FIX VERIFIED: Orchestrator is no longer stuck in PLANNING phase!")
        return True
    else:
        print("\n❌ FIX NOT WORKING: Orchestrator is still stuck or failed")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_orchestrator_fix())
    if success:
        print("\n✅ The orchestrator fix is working correctly!")
    else:
        print("\n❌ The orchestrator fix needs more work.")
