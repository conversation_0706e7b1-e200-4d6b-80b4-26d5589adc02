# Task Recovery Guide

## Problem: "Task is currently busy" Error

When you see the message "Task is currently busy. Please wait for the current operation to complete.", it means a task is stuck in a processing state.

## Quick Solutions

### 1. Automatic Recovery (Recommended)

The system now includes automatic recovery that kicks in after 5 minutes. Just wait a bit and try again.

### 2. Manual Recovery Script

Run the recovery script from the backend directory:

```bash
cd backend
python fix_stuck_tasks.py
```

This will:
- Check all tasks
- Identify stuck tasks
- Automatically recover them
- Show you the results

### 3. API Endpoints

You can also use these API endpoints directly:

#### Get Debug Information
```bash
curl http://localhost:8000/tasks/debug
```

#### Recover All Stuck Tasks
```bash
curl -X POST http://localhost:8000/tasks/recover-stuck
```

#### Reset Specific Task
```bash
curl -X POST http://localhost:8000/task/{task_id}/reset
```

#### Delete Specific Task
```bash
curl -X DELETE http://localhost:8000/task/{task_id}
```

#### Force Task Status
```bash
curl -X POST http://localhost:8000/task/{task_id}/force-status/AWAITING_COMMAND
```

Valid statuses:
- `AWAITING_COMMAND`
- `AWAITING_USER_CONFIRMATION`
- `AWAITING_USER_INPUT`
- `PROCESSING_COMMAND`
- `PLANNING`
- `EXECUTING`
- `REFINING`
- `COMPLETED`
- `FAILED`
- `ABORTED`

### 4. Frontend Debug Panel

A debug panel component has been created (`TaskDebugPanel.tsx`) that you can integrate into your frontend to:
- View all tasks and their statuses
- Identify stuck tasks
- Reset or delete tasks with a click
- Recover all stuck tasks at once

## Prevention

The system now includes:

1. **Automatic Timeout Recovery**: Tasks stuck for more than 5 minutes are automatically recovered
2. **Better State Management**: Improved task state transitions
3. **Debug Endpoints**: Easy monitoring and recovery tools

## Common Stuck States

Tasks typically get stuck in these states:
- `PROCESSING_COMMAND` - Usually from SSH command timeouts
- `PLANNING` - AI planning phase got stuck
- `EXECUTING` - Command execution phase stuck
- `REFINING` - Post-execution processing stuck

## Troubleshooting Steps

1. **First**: Wait 5-10 minutes for automatic recovery
2. **Second**: Run the recovery script: `python fix_stuck_tasks.py`
3. **Third**: Use API endpoints to manually reset specific tasks
4. **Last Resort**: Restart the backend server

## Monitoring

Use the debug endpoint to monitor task health:
```bash
# Show all tasks with details
python fix_stuck_tasks.py --show-all

# Or use curl
curl http://localhost:8000/tasks/debug | jq
```

## Integration

To add the debug panel to your frontend:

1. Import the component:
```tsx
import TaskDebugPanel from './components/TaskDebugPanel';
```

2. Add it to your main component:
```tsx
<TaskDebugPanel />
```

The panel will appear as a settings button in the bottom-right corner and can be opened when needed.

## Notes

- All recovery operations are safe and won't damage your data
- Tasks are reset to `AWAITING_COMMAND` state, allowing you to continue
- The system maintains task history and metadata during recovery
- Automatic recovery prevents most stuck task scenarios
