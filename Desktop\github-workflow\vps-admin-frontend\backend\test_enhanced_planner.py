#!/usr/bin/env python3
"""
Test the enhanced planner that generates both descriptions and commands.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from agents import PlannerAgent
from models import TaskStep, TaskPlan, StepStatus


async def test_enhanced_planner():
    """Test the enhanced PlannerAgent with command generation."""
    print("=== Testing Enhanced PlannerAgent ===")
    
    config = Config()
    planner = PlannerAgent(config)
    
    # Test with the specific example from the user
    user_prompt = "host this static web app: https://github.com/Moetez-Baklouti/portfolio"
    system_info = "Ubuntu 20.04 LTS, nginx available, git installed"
    
    try:
        print(f"User prompt: {user_prompt}")
        print(f"System info: {system_info}")
        print("\nCalling Enhanced PlannerAgent...")
        
        response = await planner.process(
            user_prompt=user_prompt,
            system_info=system_info
        )
        
        print(f"\nPlanner Response:")
        print(f"Success: {response.success}")
        print(f"Estimated Steps: {response.estimated_steps}")
        print(f"Number of plan steps: {len(response.plan_steps)}")
        
        if response.metadata.get('error'):
            print(f"Error details: {response.metadata['error']}")
        
        print("\nPlan Steps with Commands:")
        for i, step in enumerate(response.plan_steps, 1):
            print(f"  {i}. Description: {step['description']}")
            if 'command' in step:
                print(f"     Command: {step['command']}")
            else:
                print(f"     Command: MISSING!")
            print()
        
        # Test creating TaskPlan from planner response
        if response.plan_steps:
            print("Creating TaskPlan with commands...")
            steps = []
            for i, step_data in enumerate(response.plan_steps, 1):
                step = TaskStep(
                    step_number=i,
                    description=step_data['description'],
                    command=step_data.get('command'),  # Include command from planner
                    status=StepStatus.PENDING,
                    created_at=asyncio.get_event_loop().time()
                )
                steps.append(step)
            
            task_plan = TaskPlan(
                steps=steps,
                total_steps=len(steps),
                current_step=0
            )
            
            print(f"✅ TaskPlan created with {len(steps)} steps")
            print("TaskPlan steps:")
            for step in task_plan.steps:
                print(f"  Step {step.step_number}: {step.description}")
                print(f"    Command: {step.command}")
                print(f"    Status: {step.status}")
                print()
        
        return response.success and len(response.plan_steps) > 0
        
    except Exception as e:
        print(f"Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("Testing Enhanced Planner System")
    print("=" * 50)
    
    success = await test_enhanced_planner()
    
    if success:
        print("✅ Enhanced Planner test PASSED")
    else:
        print("❌ Enhanced Planner test FAILED")
    
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
