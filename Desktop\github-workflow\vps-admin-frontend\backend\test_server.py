#!/usr/bin/env python3
"""
Simple test script to start the FastAPI server
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Starting test server...")
    
    # Import the app
    from main import app
    import uvicorn
    
    print("App imported successfully")
    print("Starting uvicorn server...")
    
    # Run the server
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        log_level="info"
    )
    
except Exception as e:
    print(f"Error starting server: {e}")
    import traceback
    traceback.print_exc()
