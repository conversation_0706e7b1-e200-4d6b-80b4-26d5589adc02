# Enhanced Error Recovery System

## Overview

The VPS Admin system now includes a comprehensive recursive error recovery mechanism that automatically creates recovery plans when commands fail, addressing the specific issue where `git clone` fails due to existing directories.

## Key Features

### 1. ErrorRecoveryPlanner Agent
- **Purpose**: Creates step-by-step recovery plans for failed commands
- **Intelligence**: Analyzes error messages to determine root causes
- **Approach**: Suggests alternative methods or prerequisite fixes

### 2. Recursive Planning System
- **Automatic Trigger**: Activates when commands fail with recoverable errors
- **User Confirmation**: Each recovery step requires user approval
- **Return to Original Plan**: After recovery, continues with the original task

### 3. Enhanced Error Detection
The system triggers recovery for these error patterns:
- `already exists` - Directory/file conflicts
- `permission denied` - Access rights issues
- `not found` / `no such file` - Missing dependencies
- `connection refused` - Network connectivity
- `package not available` - Repository/package issues
- `service not found` - Service management problems
- `port already in use` - Port conflicts

## How It Works

### 1. Command Execution Flow
```
Original Command Fails
    ↓
Error Analysis
    ↓
Should Trigger Recovery? → No → Standard Retry Logic
    ↓ Yes
Create Recovery Plan
    ↓
Execute Recovery Steps (with user confirmation)
    ↓
Return to Original Plan
```

### 2. Recovery Plan Creation
When a command fails, the ErrorRecoveryPlanner:
1. Analyzes stdout, stderr, and exit status
2. Identifies the root cause
3. Creates 2-4 recovery steps
4. Each step has description + command
5. Uses alternative approaches when needed

### 3. Example Recovery Scenarios

#### Scenario 1: Directory Already Exists
**Failed Command**: `git clone https://github.com/user/portfolio /var/www/html/portfolio`
**Error**: `fatal: destination path '/var/www/html/portfolio' already exists and is not an empty directory.`

**Recovery Plan**:
1. Remove existing conflicting directory: `sudo rm -rf /var/www/html/portfolio`
2. Clone repository to correct location: `sudo git clone https://github.com/user/portfolio /var/www/html/portfolio`
3. Set proper ownership: `sudo chown -R www-data:www-data /var/www/html/portfolio`

#### Scenario 2: Permission Denied
**Failed Command**: `mkdir /var/www/html/app`
**Error**: `mkdir: cannot create directory '/var/www/html/app': Permission denied`

**Recovery Plan**:
1. Create directory with sudo: `sudo mkdir /var/www/html/app`
2. Set proper ownership: `sudo chown $USER:$USER /var/www/html/app`

#### Scenario 3: Package Not Found
**Failed Command**: `apt install docker`
**Error**: `E: Unable to locate package docker`

**Recovery Plan**:
1. Update package lists: `sudo apt update`
2. Install correct Docker package: `sudo apt install -y docker.io`
3. Start Docker service: `sudo systemctl start docker`

## Implementation Details

### New Models
- `ErrorRecoveryResponse`: Response from recovery planner
- `StepAttempt`: Enhanced with recovery tracking fields
- `TaskEntry`: Added parent/child task relationships

### New Agent
- `ErrorRecoveryPlanner`: Specialized agent for creating recovery plans

### Enhanced Orchestrator
- `_should_trigger_error_recovery()`: Determines if recovery is needed
- `_trigger_error_recovery()`: Creates and executes recovery plans
- Integrated with existing retry logic

### Frontend Updates
- New message types for recovery progress
- Visual indicators for recovery steps
- Recovery command confirmations

## Configuration

### Environment Variables
- `MAX_RETRIES_PER_STEP`: Number of retries before triggering recovery (default: 3)
- Recovery tasks have fewer retries (default: 2) to prevent infinite loops

### Recovery Triggers
The system can be configured to trigger recovery for specific error patterns by modifying the `recovery_triggers` list in `_should_trigger_error_recovery()`.

## Usage

### For Users
1. **Automatic**: Recovery triggers automatically when commands fail
2. **Confirmation**: Each recovery step requires user approval
3. **Progress**: Clear indicators show recovery progress
4. **Continuation**: Original task continues after successful recovery

### For Developers
1. **Testing**: Use `test_error_recovery.py` to test recovery scenarios
2. **Customization**: Add new recovery patterns in ErrorRecoveryPlanner
3. **Monitoring**: Recovery events are logged with detailed context

## Benefits

1. **Intelligent Problem Solving**: AI analyzes errors and suggests fixes
2. **User Control**: All recovery actions require confirmation
3. **Reduced Manual Intervention**: Common issues are automatically addressed
4. **Learning System**: Recovery patterns can be improved over time
5. **Transparent Process**: Users see exactly what recovery steps are taken

## Testing

Run the error recovery tests:
```bash
cd backend
python test_error_recovery.py
```

This tests various failure scenarios and verifies the recovery system works correctly.

## Future Enhancements

1. **Recovery History**: Track successful recovery patterns
2. **Learning System**: Improve recovery suggestions based on success rates
3. **Batch Recovery**: Handle multiple related failures together
4. **Recovery Templates**: Pre-defined recovery plans for common issues
5. **Rollback Capability**: Undo recovery steps if they cause new problems

## Error Recovery vs Standard Retry

| Feature | Standard Retry | Error Recovery |
|---------|---------------|----------------|
| Trigger | Any failure | Specific recoverable errors |
| Approach | Repeat same command | Analyze and fix root cause |
| User Input | None | Confirmation required |
| Intelligence | None | AI-powered analysis |
| Success Rate | Low for persistent issues | High for fixable problems |

The enhanced error recovery system transforms the VPS Admin from a simple command executor into an intelligent system administrator that can diagnose and fix common issues automatically.
