#!/usr/bin/env python3
"""
Simple test script for the VPS Admin Orchestrator system.
Tests the individual agents and orchestrator functionality.
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from agents import PlannerAgent, ExecutorAgent, RefinerAgent, SummarizerAgent
from models import SSHResult, StepAttempt


async def test_planner_agent():
    """Test the PlannerAgent."""
    print("=== Testing PlannerAgent ===")
    
    config = Config()
    planner = PlannerAgent(config)
    
    try:
        response = await planner.process(
            user_prompt="Install Docker and run a simple web server",
            system_info="Ubuntu 20.04 LTS, apt package manager available"
        )
        
        print(f"Planner Success: {response.success}")
        print(f"Estimated Steps: {response.estimated_steps}")
        print("Plan Steps:")
        for i, step in enumerate(response.plan_steps, 1):
            print(f"  {i}. {step['description']}")
        
        return response.success
        
    except Exception as e:
        print(f"Planner Test Failed: {e}")
        return False


async def test_executor_agent():
    """Test the ExecutorAgent."""
    print("\n=== Testing ExecutorAgent ===")
    
    config = Config()
    executor = ExecutorAgent(config)
    
    try:
        response = await executor.process(
            step_description="Update package lists",
            context_history=[],
            system_info="Ubuntu 20.04 LTS, apt package manager available"
        )
        
        print(f"Executor Success: {response.success}")
        print(f"Generated Command: {response.command}")
        print(f"Command Type: {response.command_type}")
        print(f"Security Risk: {response.security_risk}")
        
        return response.success
        
    except Exception as e:
        print(f"Executor Test Failed: {e}")
        return False


async def test_refiner_agent():
    """Test the RefinerAgent."""
    print("\n=== Testing RefinerAgent ===")
    
    config = Config()
    refiner = RefinerAgent(config)
    
    # Simulate failed attempts
    failed_attempts = [
        {
            "command": "apt install docker",
            "error_message": "E: Unable to locate package docker"
        },
        {
            "command": "sudo apt install docker",
            "error_message": "E: Unable to locate package docker"
        }
    ]
    
    try:
        response = await refiner.process(
            step_description="Install Docker",
            failed_attempts=failed_attempts
        )
        
        print(f"Refiner Success: {response.success}")
        print(f"Original Command: {response.original_command}")
        print(f"Corrected Command: {response.corrected_command}")
        print(f"Analysis: {response.analysis}")
        
        return response.success
        
    except Exception as e:
        print(f"Refiner Test Failed: {e}")
        return False


async def test_summarizer_agent():
    """Test the SummarizerAgent."""
    print("\n=== Testing SummarizerAgent ===")
    
    config = Config()
    summarizer = SummarizerAgent(config)
    
    # Simulate successful command execution
    ssh_result = SSHResult(
        stdout="Reading package lists... Done\nBuilding dependency tree... Done",
        stderr="",
        exit_status=0,
        success=True,
        command="sudo apt update"
    )
    
    try:
        response = await summarizer.process(
            step_description="Update package lists",
            command="sudo apt update",
            ssh_result=ssh_result
        )
        
        print(f"Summarizer Success: {response.success}")
        print(f"Step Summary: {response.step_summary}")
        print(f"Key Outcomes: {response.key_outcomes}")
        
        return response.success
        
    except Exception as e:
        print(f"Summarizer Test Failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("VPS Admin Orchestrator System Tests")
    print("=" * 50)
    
    # Check if we have the required environment variables
    print(os.getenv("GEMINI_MODEL"))
    if not os.getenv("GEMINI_MODEL"):
        print("ERROR: GEMINI_API_KEY environment variable not set")
        print("Please set your Gemini API key to run these tests")
        return
    
    tests = [
        ("PlannerAgent", test_planner_agent),
        ("ExecutorAgent", test_executor_agent),
        ("RefinerAgent", test_refiner_agent),
        ("SummarizerAgent", test_summarizer_agent),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"Test {test_name} crashed: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 50)
    print("TEST RESULTS SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20} {status}")
        if not result:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 All tests PASSED! The orchestrator system is ready.")
    else:
        print("❌ Some tests FAILED. Please check the configuration and try again.")
    
    return all_passed


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Test runner crashed: {e}")
        sys.exit(1)
