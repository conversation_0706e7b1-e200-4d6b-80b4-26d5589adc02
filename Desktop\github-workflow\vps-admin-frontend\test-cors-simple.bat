@echo off
echo ========================================
echo Simple CORS Test Server
echo ========================================
echo.

echo Killing all Python processes...
taskkill /F /IM python.exe /T >nul 2>&1
taskkill /F /IM python3.exe /T >nul 2>&1

echo Waiting for processes to stop...
timeout /t 3 /nobreak >nul

echo Navigating to backend directory...
if not exist "backend" (
    echo ERROR: Backend directory not found!
    pause
    exit /b 1
)

cd backend

echo Starting simple CORS test server...
echo.
echo ========================================
echo Simple CORS Test Server Starting...
echo This will test if CORS works at all.
echo Try the frontend now - it should work!
echo Press Ctrl+C to stop when done testing.
echo ========================================
echo.

python test_cors_server.py

pause
