"""
Task manager module for VPS AI Admin Backend.
Handles task storage, lifecycle management, and progress tracking.
"""

import asyncio
import uuid
import traceback
from typing import Dict, Any, Optional, List

from models import TaskEntry, TaskMetadata, SSHResult, TaskPlan
from ai_client import AIClient
from system_scanner import SystemScanner


class TaskManager:
    """Manager for task storage and lifecycle."""

    def __init__(self, config=None):
        self.tasks: Dict[str, TaskEntry] = {}
        self.config = config
        print("In-memory task storage initialized.")

    def create_task(self, initial_prompt: str, metadata: Optional[TaskMetadata] = None) -> str:
        """Create a new task and return task ID."""
        task_id = str(uuid.uuid4())

        # Extract metadata if provided
        task_title = metadata.title if metadata else f"Task {task_id[:8]}"
        task_description = metadata.description if metadata else initial_prompt
        task_priority = metadata.priority if metadata else "medium"

        # Create task entry
        task_entry = TaskEntry(
            id=task_id,
            title=task_title,
            description=task_description,
            priority=task_priority,
            history=[],
            chat=None,
            status="INITIALIZING",
            command_to_confirm=None,
            system_info="System scan pending...",
            created_at=asyncio.get_event_loop().time(),
            current_step=0,
            estimated_steps=5,  # Default estimate, AI can update this
            commands_executed=0,
            commands_successful=0,
            commands_failed=0,
            recent_commands=[],
            metadata=metadata.model_dump() if metadata else {},
            # Initialize orchestrator fields
            original_prompt=initial_prompt,
            use_orchestrator=self.config.USE_ORCHESTRATOR if self.config else True,
            main_context_history=[],
            current_step_attempts=[],
            task_plan=None,
            final_output="",
            max_retries_per_step=self.config.MAX_RETRIES_PER_STEP if self.config else 3
        )

        self.tasks[task_id] = task_entry
        print(f"DEBUG: Enhanced task {task_id} created with title: '{task_title}'.")
        return task_id

    async def initialize_task(self, task_id: str, initial_prompt: str,
                            ai_client: AIClient, system_scanner: SystemScanner):
        """Initialize task with system scan and AI chat session."""
        if task_id not in self.tasks:
            raise ValueError(f"Task {task_id} not found")

        task = self.tasks[task_id]

        try:
            print(f"DEBUG: Performing initial scan for task {task_id}...")
            system_info = await system_scanner.perform_initial_scan()
            task.system_info = system_info

            # Create initial chat history
            initial_history = ai_client.create_initial_history(system_info, initial_prompt)

            print(f"DEBUG: Initializing Gemini chat for task {task_id} with structured history...")
            chat = ai_client.create_chat_session(initial_history)
            task.chat = chat
            task.status = "AWAITING_COMMAND"  # Set status after successful chat init

            print(f"INFO: Task {task_id} initialized. Status: AWAITING_COMMAND.")

        except Exception as e:
            print(f"CRITICAL ERROR during task initialization: {type(e).__name__}: {e}\n{traceback.format_exc()}")
            # Clean up failed task
            if task_id in self.tasks:
                del self.tasks[task_id]
                print(f"INFO: Cleaned up failed task entry {task_id}")
            raise

    def get_task(self, task_id: str) -> Optional[TaskEntry]:
        """Get task by ID."""
        return self.tasks.get(task_id)

    def task_exists(self, task_id: str) -> bool:
        """Check if task exists."""
        return task_id in self.tasks

    def update_task_status(self, task_id: str, status: str):
        """Update task status."""
        if task_id in self.tasks:
            self.tasks[task_id].status = status

    def set_command_to_confirm(self, task_id: str, command: str):
        """Set command awaiting confirmation."""
        if task_id in self.tasks:
            self.tasks[task_id].command_to_confirm = command

    def clear_command_to_confirm(self, task_id: str):
        """Clear command awaiting confirmation."""
        if task_id in self.tasks:
            self.tasks[task_id].command_to_confirm = None

    def get_command_to_confirm(self, task_id: str) -> Optional[str]:
        """Get command awaiting confirmation."""
        task = self.tasks.get(task_id)
        return task.command_to_confirm if task else None

    def increment_step(self, task_id: str):
        """Increment current step counter."""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.current_step = min(task.current_step + 1, task.estimated_steps)

    def update_progress(self, task_id: str, current_step: int, total_steps: Optional[int] = None):
        """Update task progress."""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.current_step = current_step
            if total_steps is not None:
                task.estimated_steps = total_steps

    def add_recent_command(self, task_id: str, command: str):
        """Add command to recent commands list."""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.recent_commands.append(command)
            # Keep only last 5 commands
            task.recent_commands = task.recent_commands[-5:]

    def get_recent_commands(self, task_id: str) -> List[str]:
        """Get recent commands for task."""
        task = self.tasks.get(task_id)
        return task.recent_commands if task else []

    def record_command_execution(self, task_id: str, ssh_result: SSHResult):
        """Record command execution statistics."""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.commands_executed += 1
            if ssh_result.success:
                task.commands_successful += 1
            else:
                task.commands_failed += 1

    def is_task_busy(self, task_id: str) -> bool:
        """Check if task is currently busy processing a command."""
        task = self.tasks.get(task_id)
        return task.status == "PROCESSING_COMMAND" if task else False

    def is_task_awaiting_confirmation(self, task_id: str) -> bool:
        """Check if task is awaiting user confirmation."""
        task = self.tasks.get(task_id)
        return task.status == "AWAITING_USER_CONFIRMATION" if task else False

    def is_task_terminal(self, task_id: str) -> bool:
        """Check if task is in a terminal state."""
        task = self.tasks.get(task_id)
        if not task:
            return True
        return task.status in ["COMPLETED", "FAILED", "ABORTED"]

    def get_task_status_dict(self, task_id: str) -> Dict[str, Any]:
        """Get task status as dictionary for API response."""
        task = self.tasks.get(task_id)
        if not task:
            raise ValueError(f"Task {task_id} not found")

        current_history = []
        if task.chat:
            try:
                current_history = [
                    {
                        'role': msg.role,
                        'parts': [part.text for part in msg.parts]
                    }
                    for msg in task.chat.history
                ]
            except Exception as hist_err:
                print(f"WARNING: Could not get history from chat object {task_id}: {hist_err}")
                current_history = [{"role": "system", "parts": ["Error retrieving history."]}]

        # Prepare task plan data for response
        task_plan_data = None
        if task.task_plan:
            task_plan_data = {
                "steps": [
                    {
                        "step_number": step.step_number,
                        "description": step.description,
                        "status": step.status,
                        "command": step.command,
                        "summary": step.summary,
                        "attempts": len(step.attempts)
                    }
                    for step in task.task_plan.steps
                ],
                "total_steps": task.task_plan.total_steps,
                "current_step": task.task_plan.current_step
            }

        return {
            "task_id": task.id,
            "status": task.status,
            "history": current_history,
            "command_to_confirm": task.command_to_confirm,
            "metadata": {
                "title": task.title,
                "description": task.description,
                "priority": task.priority,
                "created_at": task.created_at,
                "current_step": task.current_step,
                "estimated_steps": task.estimated_steps,
                "commands_executed": task.commands_executed,
                "commands_successful": task.commands_successful,
                "commands_failed": task.commands_failed,
                **task.metadata
            },
            "progress": task.current_step / task.estimated_steps if task.estimated_steps > 0 else 0.0,
            "step_count": task.current_step,
            "total_steps": task.estimated_steps,
            # New orchestrator fields
            "use_orchestrator": task.use_orchestrator,
            "task_plan": task_plan_data,
            "main_context_history": task.main_context_history,
            "current_step_attempts": len(task.current_step_attempts),
            "final_output": task.final_output
        }

    def delete_task(self, task_id: str):
        """Delete a task."""
        if task_id in self.tasks:
            del self.tasks[task_id]
            print(f"INFO: Deleted task {task_id}")

    def get_all_tasks(self) -> Dict[str, TaskEntry]:
        """Get all tasks."""
        return self.tasks.copy()

    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """Clean up old tasks (optional maintenance function)."""
        current_time = asyncio.get_event_loop().time()
        max_age_seconds = max_age_hours * 3600

        tasks_to_delete = []
        for task_id, task in self.tasks.items():
            if current_time - task.created_at > max_age_seconds:
                tasks_to_delete.append(task_id)

        for task_id in tasks_to_delete:
            self.delete_task(task_id)

        if tasks_to_delete:
            print(f"INFO: Cleaned up {len(tasks_to_delete)} old tasks")

    # New methods for Orchestrator support

    def should_use_orchestrator(self, task_id: str) -> bool:
        """Check if task should use the new orchestrator system."""
        task = self.tasks.get(task_id)
        return task.use_orchestrator if task else False

    def set_orchestrator_mode(self, task_id: str, use_orchestrator: bool):
        """Enable or disable orchestrator mode for a task."""
        if task_id in self.tasks:
            self.tasks[task_id].use_orchestrator = use_orchestrator

    def get_task_plan(self, task_id: str) -> Optional[TaskPlan]:
        """Get the task plan for orchestrator tasks."""
        task = self.tasks.get(task_id)
        return task.task_plan if task else None

    def update_task_plan(self, task_id: str, task_plan: TaskPlan):
        """Update the task plan for orchestrator tasks."""
        if task_id in self.tasks:
            self.tasks[task_id].task_plan = task_plan

    def get_main_context_history(self, task_id: str) -> List[str]:
        """Get the main context history for orchestrator tasks."""
        task = self.tasks.get(task_id)
        return task.main_context_history if task else []

    def add_to_main_context(self, task_id: str, context_item: str):
        """Add an item to the main context history."""
        if task_id in self.tasks:
            self.tasks[task_id].main_context_history.append(context_item)

    def get_current_step_attempts(self, task_id: str) -> List[Any]:
        """Get current step attempts for orchestrator tasks."""
        task = self.tasks.get(task_id)
        return task.current_step_attempts if task else []

    def clear_current_step_attempts(self, task_id: str):
        """Clear current step attempts (when moving to next step)."""
        if task_id in self.tasks:
            self.tasks[task_id].current_step_attempts = []

    def set_final_output(self, task_id: str, output: str):
        """Set the final output for the task."""
        if task_id in self.tasks:
            self.tasks[task_id].final_output = output

    # Task recovery and debugging methods

    def force_task_status(self, task_id: str, new_status: str) -> bool:
        """Force update task status (for debugging/recovery)."""
        if task_id in self.tasks:
            old_status = self.tasks[task_id].status
            self.tasks[task_id].status = new_status
            print(f"INFO: Forced task {task_id} status from '{old_status}' to '{new_status}'")
            return True
        return False

    def reset_stuck_task(self, task_id: str) -> bool:
        """Reset a stuck task to AWAITING_COMMAND state."""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            old_status = task.status

            # Clear any pending confirmations
            task.command_to_confirm = None

            # CRITICAL FIX: Reset ALL orchestrator-specific fields if stuck
            if hasattr(task, 'awaiting_step_confirmation'):
                task.awaiting_step_confirmation = False
            if hasattr(task, 'current_step_command'):
                task.current_step_command = None
            if hasattr(task, 'current_step_description'):
                task.current_step_description = None
            if hasattr(task, 'current_step_number'):
                task.current_step_number = 0
            if hasattr(task, 'current_step_attempt'):
                task.current_step_attempt = 1

            # CRITICAL FIX: Clear error recovery state if stuck
            if hasattr(task, 'in_error_recovery'):
                print(f"INFO: Clearing error recovery state for stuck task {task_id}")
                task.in_error_recovery = False
                task.recovery_plan = []
                task.current_recovery_step = 0
                task.failed_step_number = 0

            # Set to a safe state
            task.status = "AWAITING_COMMAND"

            print(f"INFO: Reset stuck task {task_id} from '{old_status}' to 'AWAITING_COMMAND'")
            print(f"INFO: Cleared all confirmation and recovery states for task {task_id}")
            return True
        return False

    def get_stuck_tasks(self) -> List[str]:
        """Get list of task IDs that appear to be stuck."""
        stuck_statuses = ["PROCESSING_COMMAND", "PLANNING", "EXECUTING", "REFINING"]
        current_time = asyncio.get_event_loop().time()
        stuck_tasks = []

        for task_id, task in self.tasks.items():
            if task.status in stuck_statuses:
                # Consider task stuck if it's been in this state for more than 5 minutes
                time_in_state = current_time - task.created_at
                if time_in_state > 300:  # 5 minutes
                    stuck_tasks.append(task_id)

        return stuck_tasks

    def auto_recover_stuck_tasks(self) -> int:
        """Automatically recover stuck tasks. Returns number of tasks recovered."""
        stuck_tasks = self.get_stuck_tasks()
        recovered_count = 0

        for task_id in stuck_tasks:
            if self.reset_stuck_task(task_id):
                recovered_count += 1

        if recovered_count > 0:
            print(f"INFO: Auto-recovered {recovered_count} stuck tasks")

        return recovered_count

    def get_all_task_statuses(self) -> Dict[str, Dict[str, Any]]:
        """Get summary of all tasks and their statuses (for debugging)."""
        summary = {}
        current_time = asyncio.get_event_loop().time()

        for task_id, task in self.tasks.items():
            age_minutes = (current_time - task.created_at) / 60
            summary[task_id] = {
                "status": task.status,
                "title": task.title,
                "age_minutes": round(age_minutes, 1),
                "current_step": task.current_step,
                "estimated_steps": task.estimated_steps,
                "commands_executed": task.commands_executed,
                "has_command_to_confirm": task.command_to_confirm is not None,
                "use_orchestrator": getattr(task, 'use_orchestrator', False)
            }

        return summary

    # Error recovery task management methods

    def create_recovery_task(self, parent_task_id: str, failed_step_info: Dict[str, Any],
                           recovery_prompt: str, metadata: Optional[TaskMetadata] = None) -> str:
        """Create a recovery task for a failed step."""
        recovery_task_id = str(uuid.uuid4())

        # Extract metadata if provided
        task_title = f"Recovery for {parent_task_id[:8]}"
        task_description = f"Recovery task: {recovery_prompt}"
        task_priority = "high"  # Recovery tasks have high priority

        # Create recovery task entry
        recovery_task = TaskEntry(
            id=recovery_task_id,
            title=task_title,
            description=task_description,
            priority=task_priority,
            history=[],
            chat=None,
            status="INITIALIZING",
            command_to_confirm=None,
            system_info="System scan pending...",
            created_at=asyncio.get_event_loop().time(),
            current_step=0,
            estimated_steps=3,  # Recovery tasks are typically shorter
            commands_executed=0,
            commands_successful=0,
            commands_failed=0,
            recent_commands=[],
            metadata=metadata.model_dump() if metadata else {},
            # Initialize orchestrator fields
            original_prompt=recovery_prompt,
            use_orchestrator=True,  # Recovery tasks always use orchestrator
            main_context_history=[],
            current_step_attempts=[],
            task_plan=None,
            final_output="",
            max_retries_per_step=2,  # Fewer retries for recovery tasks
            # Recovery-specific fields
            parent_task_id=parent_task_id,
            child_recovery_tasks=[],
            is_recovery_task=True,
            failed_step_info=failed_step_info
        )

        self.tasks[recovery_task_id] = recovery_task

        # Update parent task to track this recovery task
        if parent_task_id in self.tasks:
            self.tasks[parent_task_id].child_recovery_tasks.append(recovery_task_id)

        print(f"DEBUG: Recovery task {recovery_task_id} created for parent {parent_task_id}")
        return recovery_task_id

    def is_recovery_task(self, task_id: str) -> bool:
        """Check if a task is a recovery task."""
        task = self.tasks.get(task_id)
        return getattr(task, 'is_recovery_task', False) if task else False

    def get_parent_task_id(self, task_id: str) -> Optional[str]:
        """Get the parent task ID for a recovery task."""
        task = self.tasks.get(task_id)
        return getattr(task, 'parent_task_id', None) if task else None

    def get_child_recovery_tasks(self, task_id: str) -> List[str]:
        """Get list of child recovery task IDs for a task."""
        task = self.tasks.get(task_id)
        return getattr(task, 'child_recovery_tasks', []) if task else []

    def mark_recovery_completed(self, recovery_task_id: str, success: bool):
        """Mark a recovery task as completed and update parent task."""
        if recovery_task_id not in self.tasks:
            return

        recovery_task = self.tasks[recovery_task_id]
        parent_task_id = getattr(recovery_task, 'parent_task_id', None)

        if success:
            recovery_task.status = "COMPLETED"
            recovery_task.final_output = "Recovery completed successfully"
        else:
            recovery_task.status = "FAILED"
            recovery_task.final_output = "Recovery failed"

        # Update parent task if recovery was successful
        if parent_task_id and parent_task_id in self.tasks and success:
            parent_task = self.tasks[parent_task_id]
            # Reset parent task to continue execution
            parent_task.status = "EXECUTING"
            print(f"DEBUG: Recovery {recovery_task_id} completed, parent {parent_task_id} can continue")

    def get_failed_step_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the failed step info that triggered recovery."""
        task = self.tasks.get(task_id)
        return getattr(task, 'failed_step_info', None) if task else None
