#!/usr/bin/env python3
"""
Test script to verify the error recovery fix.
This tests that the system properly triggers error recovery after max retries.
"""

import asyncio
import sys
import os
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from orchestrator import TaskOrchestrator
from models import TaskEntry, TaskPlan, TaskStep, StepStatus, SSHResult
from ssh_client import SSHClient


async def test_error_recovery_fix():
    """Test that error recovery is triggered after max retries."""
    print("\n=== Testing Error Recovery Fix ===")
    
    # Setup
    config = Config()
    ssh_client = SSHClient(config)
    orchestrator = TaskOrchestrator(config, ssh_client)
    
    # Create a test task
    task = TaskEntry(
        id="test-task-001",
        title="Test Error Recovery",
        description="Test that error recovery triggers after max retries",
        priority="medium",
        history=[],
        status="EXECUTING",
        system_info="Test system",
        created_at=asyncio.get_event_loop().time(),
        original_prompt="Test error recovery",
        max_retries_per_step=3,  # Set to 3 for testing
        current_step_attempts=[]
    )
    
    # Create a test step that will fail
    step = TaskStep(
        step_number=1,
        description="Clone portfolio repository to web directory",
        status=StepStatus.EXECUTING,
        command="git clone https://github.com/user/portfolio /var/www/html/portfolio"
    )
    
    # Create a task plan
    task.task_plan = TaskPlan(
        steps=[step],
        total_steps=1,
        current_step=0
    )
    
    print(f"Task ID: {task.id}")
    print(f"Max retries per step: {task.max_retries_per_step}")
    print(f"Test command: {step.command}")
    print(f"Expected error: 'already exists' (should trigger recovery)")
    
    # Simulate the error scenario
    print("\n--- Simulating Command Failures ---")
    
    # Create a mock SSH result that will trigger error recovery
    ssh_result = SSHResult(
        stdout="",
        stderr="fatal: destination path '/var/www/html/portfolio' already exists and is not an empty directory.",
        exit_status=128,
        success=False,
        command=step.command
    )
    
    # Test 1: Check if error recovery should be triggered
    should_recover = orchestrator._should_trigger_error_recovery(ssh_result)
    print(f"✓ Should trigger recovery: {should_recover}")
    assert should_recover, "Error recovery should be triggered for 'already exists' error"
    
    # Test 2: Simulate multiple failed attempts
    print("\n--- Simulating Multiple Failed Attempts ---")
    
    # Add failed attempts to simulate retries
    from models import StepAttempt
    
    for attempt_num in range(1, task.max_retries_per_step + 1):
        attempt = StepAttempt(
            attempt_number=attempt_num,
            command=step.command,
            ssh_result=ssh_result,
            error_message=ssh_result.stderr,
            stdout=ssh_result.stdout,
            stderr=ssh_result.stderr,
            exit_status=ssh_result.exit_status,
            timestamp=asyncio.get_event_loop().time()
        )
        task.current_step_attempts.append(attempt)
        print(f"  Attempt {attempt_num}: FAILED - {ssh_result.stderr[:50]}...")
    
    print(f"✓ Total failed attempts: {len(task.current_step_attempts)}")
    print(f"✓ Max retries reached: {len(task.current_step_attempts) >= task.max_retries_per_step}")
    
    # Test 3: Test error recovery trigger
    print("\n--- Testing Error Recovery Trigger ---")
    
    try:
        # This should trigger error recovery
        events = []
        async for event in orchestrator._trigger_error_recovery(task, step, step.command, ssh_result):
            event_data = json.loads(event.data)
            events.append(event_data)
            print(f"  Event: {event_data['type']} - {event_data.get('content', '')[:60]}...")
        
        # Check if recovery was triggered
        recovery_start_events = [e for e in events if e['type'] == 'error_recovery_start']
        recovery_plan_events = [e for e in events if e['type'] == 'error_recovery_plan']
        
        if recovery_start_events:
            print("✅ Error recovery START event triggered successfully!")
        else:
            print("❌ Error recovery START event was NOT triggered")
            
        if recovery_plan_events:
            print("✅ Error recovery PLAN event triggered successfully!")
            metadata = recovery_plan_events[0].get('metadata', {})
            print(f"  Recovery steps: {metadata.get('recoverySteps', 0)}")
            print(f"  Recovery approach: {metadata.get('recoveryApproach', 'unknown')}")
        else:
            print("❌ Error recovery PLAN event was NOT triggered")
        
        # Test 4: Verify the fix - attempt counter should not reset
        print("\n--- Testing Attempt Counter Fix ---")
        
        # Reset for clean test
        task.current_step_attempts = []
        task.awaiting_step_confirmation = False
        task.current_step_attempt = 1
        
        # Simulate the retry flow
        print("  Simulating retry flow...")
        
        # Add one failed attempt
        attempt1 = StepAttempt(
            attempt_number=1,
            command=step.command,
            ssh_result=ssh_result,
            error_message=ssh_result.stderr,
            stdout=ssh_result.stdout,
            stderr=ssh_result.stderr,
            exit_status=ssh_result.exit_status,
            timestamp=asyncio.get_event_loop().time()
        )
        task.current_step_attempts.append(attempt1)
        
        # Calculate next attempt using the fixed logic
        next_attempt = len(task.current_step_attempts) + 1
        print(f"  Current attempts: {len(task.current_step_attempts)}")
        print(f"  Next attempt should be: {next_attempt}")
        
        # This should be 2, not 1 (which was the bug)
        assert next_attempt == 2, f"Next attempt should be 2, but got {next_attempt}"
        print("✅ Attempt counter fix verified - no reset to 1!")
        
        print("\n🎉 All error recovery fix tests PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Error recovery test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the error recovery fix test."""
    print("Testing Error Recovery Fix...")
    print("This verifies that the infinite loop bug is fixed.")
    
    try:
        success = await test_error_recovery_fix()
        
        print("\n" + "="*60)
        print("ERROR RECOVERY FIX TEST SUMMARY")
        print("="*60)
        
        if success:
            print("✅ ERROR RECOVERY FIX VERIFIED!")
            print("   - Attempt counter no longer resets to 1")
            print("   - Error recovery triggers after max retries")
            print("   - No more infinite command confirmation loops")
        else:
            print("❌ ERROR RECOVERY FIX FAILED!")
            print("   - The fix may not be working correctly")
        
        return success
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
