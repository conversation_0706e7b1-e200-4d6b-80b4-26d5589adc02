/**
 * Chat header component for VPS Admin Chat
 */

import React from 'react';
import { Sparkles, Target } from 'lucide-react';

interface ChatHeaderProps {
  isTaskActive: boolean;
  taskId: string | null;
  showStatsPanel?: boolean;
  onToggleStats?: () => void;
  className?: string;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  isTaskActive,
  taskId,
  showStatsPanel = false,
  onToggleStats,
  className = ""
}) => {
  return (
    <div className={`p-3 border-b border-gray-200 bg-white flex-shrink-0 flex items-center justify-between ${className}`}>
      {/* Left side - Icon and Title */}
      <div className="flex items-center gap-2 flex-1">
        <Sparkles size={18} className="text-teal-500"/>
        <h1 className="text-sm lg:text-md font-semibold text-gray-700">
          VPS Admin
        </h1>
      </div>

      {/* Right side - Stats button and Status */}
      <div className="flex items-center gap-2">
        {/* Stats Toggle Button */}
        {onToggleStats && (
          <button
            onClick={onToggleStats}
            className={`p-1.5 rounded-lg transition-colors ${
              showStatsPanel
                ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
            title="Toggle Statistics Panel"
          >
            <Target size={14} />
          </button>
        )}

        {/* Status Indicator */}
        <span className={`text-xs font-medium px-2 py-1 rounded-full ${
          isTaskActive
            ? 'bg-green-100 text-green-800 ring-1 ring-green-200'
            : 'bg-gray-100 text-gray-600 ring-1 ring-gray-200'
        }`}>
          {isTaskActive ? 'Active' : 'Inactive'} {taskId ? `(${taskId.substring(0,6)})` : ''}
        </span>
      </div>
    </div>
  );
};

export default ChatHeader;
