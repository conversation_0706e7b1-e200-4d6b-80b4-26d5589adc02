#!/usr/bin/env python3
"""
Test script to verify Gemini model configuration and API connectivity.
"""

import os
import dotenv
import google.generativeai as genai

def test_gemini_model():
    """Test the Gemini model configuration."""
    
    # Load environment variables
    dotenv.load_dotenv()
    
    api_key = os.getenv("GEMINI_API_KEY")
    model_name = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
    
    print(f"API Key: {'*' * 20 if api_key else 'NOT SET'}")
    print(f"Model Name: {model_name}")
    
    if not api_key:
        print("❌ GEMINI_API_KEY not found in environment")
        return False
    
    try:
        # Configure the API
        genai.configure(api_key=api_key)
        print("✅ API configured successfully")
        
        # List available models
        print("\nAvailable models:")
        for model in genai.list_models():
            if 'generateContent' in model.supported_generation_methods:
                print(f"  - {model.name}")
        
        # Test the specific model
        print(f"\nTesting model: {model_name}")
        model = genai.GenerativeModel(model_name)
        
        # Generate a simple response
        response = model.generate_content("Say hello")
        
        if response and hasattr(response, 'text'):
            print(f"✅ Model test successful!")
            print(f"Response: {response.text}")
            return True
        else:
            print("❌ Model response was empty or blocked")
            return False
            
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_gemini_model()
    if success:
        print("\n🎉 Gemini model configuration is working correctly!")
    else:
        print("\n❌ Gemini model configuration needs fixing.")
