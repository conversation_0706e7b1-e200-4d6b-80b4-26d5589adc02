#!/usr/bin/env python3
"""
Test script to verify the orchestrator is working correctly without SSH.
"""

import asyncio
from config import Config
from agents import PlannerAgent
from models import TaskEntry, TaskPlan, TaskStep, StepStatus

async def test_orchestrator_planning():
    """Test just the planning phase of the orchestrator."""
    
    print("=== Testing Orchestrator Planning Phase ===")
    
    config = Config()
    planner = PlannerAgent(config)
    
    # Create a mock task entry
    task = TaskEntry(
        id="test-task-123",
        title="Test Portfolio Hosting",
        description="Host a static web app",
        priority="medium",
        history=[],
        chat=None,
        status="PLANNING",
        command_to_confirm=None,
        system_info="Ubuntu 20.04 LTS, Apache web server available, git installed",
        created_at=asyncio.get_event_loop().time(),
        current_step=0,
        estimated_steps=0,
        commands_executed=0,
        commands_successful=0,
        commands_failed=0,
        recent_commands=[],
        metadata={},
        original_prompt="host this static web app: https://github.com/Moetez-Ba<PERSON>louti/portfolio",
        use_orchestrator=True,
        main_context_history=[],
        current_step_attempts=[],
        task_plan=None,
        final_output="",
        max_retries_per_step=3
    )
    
    user_prompt = "host this static web app: https://github.com/Moetez-Baklouti/portfolio"
    
    try:
        print(f"User prompt: {user_prompt}")
        print(f"System info: {task.system_info}")
        print("\nCalling PlannerAgent...")
        
        # Test the planner
        planner_response = await planner.process(
            user_prompt=user_prompt,
            system_info=task.system_info
        )
        
        print(f"\nPlanner Response:")
        print(f"Success: {planner_response.success}")
        print(f"Estimated Steps: {planner_response.estimated_steps}")
        print(f"Number of plan steps: {len(planner_response.plan_steps)}")
        
        if planner_response.metadata.get('error'):
            print(f"Error details: {planner_response.metadata['error']}")
        
        print("\nPlan Steps:")
        for i, step in enumerate(planner_response.plan_steps, 1):
            print(f"  {i}. {step['description']}")
        
        # Test creating TaskPlan from planner response
        if planner_response.plan_steps:
            print("\nCreating TaskPlan...")
            steps = []
            for i, step_data in enumerate(planner_response.plan_steps, 1):
                step = TaskStep(
                    step_number=i,
                    description=step_data['description'],
                    status=StepStatus.PENDING,
                    created_at=asyncio.get_event_loop().time()
                )
                steps.append(step)
            
            task.task_plan = TaskPlan(
                steps=steps,
                total_steps=len(steps),
                current_step=0
            )
            
            print(f"✅ TaskPlan created with {len(steps)} steps")
            print("TaskPlan steps:")
            for step in task.task_plan.steps:
                print(f"  Step {step.step_number}: {step.description} [{step.status}]")
        
        return planner_response.success and len(planner_response.plan_steps) > 0
        
    except Exception as e:
        print(f"Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the orchestrator planning test."""
    success = await test_orchestrator_planning()
    
    if success:
        print("\n🎉 Orchestrator planning phase is working correctly!")
        print("The JSON parsing issue has been resolved.")
    else:
        print("\n❌ Orchestrator planning test failed")

if __name__ == "__main__":
    asyncio.run(main())
