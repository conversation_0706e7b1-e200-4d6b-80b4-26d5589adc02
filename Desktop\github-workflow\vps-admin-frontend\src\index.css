@import "tailwindcss";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile-first responsive design improvements */
@layer base {
  html {
    /* Prevent zoom on input focus on iOS */
    -webkit-text-size-adjust: 100%;
  }

  body {
    /* Improve touch scrolling on mobile */
    -webkit-overflow-scrolling: touch;
    /* Prevent horizontal scroll */
    overflow-x: hidden;
  }

  /* Improve touch targets */
  button, [role="button"] {
    touch-action: manipulation;
  }
}

@layer utilities {
  /* Touch-friendly utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Safe area padding for mobile devices with notches */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Prevent text selection on UI elements */
  .select-none-touch {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
  }
}

/* Markdown content improvements */
@layer components {
  /* Better text wrapping for markdown content */
  .markdown-content {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Inline code wrapping */
  .markdown-content code {
    word-break: break-word;
    white-space: pre-wrap;
  }

  /* Ensure lists wrap properly */
  .markdown-content li {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
}

.clip-path-message-tail {
  clip-path: polygon(0 0, 0% 100%, 100% 0);
}
.clip-path-message-tail-user {
  clip-path: polygon(100% 0, 0 0, 100% 100%);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
.animate-fadeIn {
  animation: fadeIn 0.3s ease-in;
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Improve scrolling performance on mobile */
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
  }

  /* Ensure minimum touch target size for important buttons only */
  .touch-target-large {
    min-height: 44px;
    min-width: 44px;
  }

  /* Fix button sizing issues on mobile */
  button {
    box-sizing: border-box;
  }
}