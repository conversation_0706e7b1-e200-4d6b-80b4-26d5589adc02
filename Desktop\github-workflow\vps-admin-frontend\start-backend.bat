@echo off
echo ========================================
echo VPS Admin Backend Startup Script
echo ========================================
echo.

echo Checking if backend directory exists...
if not exist "backend" (
    echo ERROR: Backend directory not found!
    echo Please run this script from the vps-admin-frontend directory.
    pause
    exit /b 1
)

echo Navigating to backend directory...
cd backend

echo.
echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found. Trying python3...
    python3 --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ERROR: Python is not installed or not in PATH.
        echo Please install Python from https://python.org
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=python3
    )
) else (
    set PYTHON_CMD=python
)

echo Python found: %PYTHON_CMD%
echo.

echo Checking if requirements.txt exists...
if not exist "requirements.txt" (
    echo ERROR: requirements.txt not found in backend directory!
    pause
    exit /b 1
)

echo Installing dependencies...
%PYTHON_CMD% -m pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies.
    echo Try running as administrator or check your internet connection.
    pause
    exit /b 1
)

echo.
echo Checking if main.py exists...
if not exist "main.py" (
    echo ERROR: main.py not found in backend directory!
    pause
    exit /b 1
)

echo.
echo Starting backend server...
echo.
echo ========================================
echo Backend server is starting...
echo Keep this window open while using the frontend.
echo Press Ctrl+C to stop the server.
echo ========================================
echo.

%PYTHON_CMD% main.py

echo.
echo Backend server stopped.
pause
