/**
 * MarkdownRenderer component for rendering markdown content
 */

import React from 'react';
import ReactMarkdown from 'react-markdown';
import { MarkdownRendererProps } from '../types';

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = "max-w-full overflow-hidden"
}) => {
  return (
    <div className={`markdown-content ${className}`}>
      <ReactMarkdown
        components={{
          // Custom styling for markdown elements
          p: ({ children }) => <p className="mb-2 last:mb-0 break-words">{children}</p>,
          strong: ({ children }) => <strong className="font-bold">{children}</strong>,
          em: ({ children }) => <em className="italic">{children}</em>,
          code: ({ children }) => <code className="bg-black/10 dark:bg-white/20 px-1 py-0.5 rounded text-sm font-mono border inline-block mr-1">{children}</code>,
          pre: ({ children }) => <pre className="bg-black/5 dark:bg-white/10 p-2 rounded mt-2 mb-2 overflow-x-auto max-w-full border">{children}</pre>,
          ul: ({ children }) => <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>,
          ol: ({ children }) => <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>,
          li: ({ children }) => <li className="ml-2 break-words leading-relaxed">{children}</li>,
          h1: ({ children }) => <h1 className="text-lg font-bold mb-2 break-words">{children}</h1>,
          h2: ({ children }) => <h2 className="text-base font-bold mb-2 break-words">{children}</h2>,
          h3: ({ children }) => <h3 className="text-sm font-bold mb-1 break-words">{children}</h3>,
          blockquote: ({ children }) => <blockquote className="border-l-2 border-current/30 pl-3 italic mb-2 break-words">{children}</blockquote>,
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;
