/**
 * Constants and configuration for VPS Admin Chat application
 */

// API Configuration
export const API_BASE_URL = "http://localhost:8000";

// UI Configuration
export const MAX_COMMAND_HISTORY = 50;
export const MAX_MESSAGES_DISPLAY = 1000;
export const AUTO_SCROLL_THRESHOLD = 100;

// Timing Configuration
export const PROGRESS_UPDATE_DELAY = 500;
export const STATS_UPDATE_INTERVAL = 1000;

// Common commands for autocomplete
export const COMMON_COMMANDS = [
  'sudo systemctl status',
  'sudo systemctl start',
  'sudo systemctl stop',
  'sudo systemctl restart',
  'sudo systemctl enable',
  'sudo systemctl disable',
  'sudo apt update',
  'sudo apt upgrade',
  'sudo apt install',
  'sudo apt remove',
  'sudo apt autoremove',
  'sudo apt search',
  'sudo yum update',
  'sudo yum install',
  'sudo yum remove',
  'sudo dnf update',
  'sudo dnf install',
  'sudo dnf remove',
  'ls -la',
  'ls -lah',
  'cd',
  'pwd',
  'ps aux',
  'ps -ef',
  'top',
  'htop',
  'df -h',
  'du -sh',
  'free -h',
  'uname -a',
  'whoami',
  'id',
  'groups',
  'sudo tail -f /var/log/syslog',
  'sudo tail -f /var/log/auth.log',
  'sudo tail -f /var/log/nginx/access.log',
  'sudo tail -f /var/log/nginx/error.log',
  'sudo journalctl -f',
  'sudo journalctl -u',
  'netstat -tulpn',
  'ss -tulpn',
  'lsof -i',
  'sudo iptables -L',
  'sudo ufw status',
  'sudo ufw enable',
  'sudo ufw disable',
  'sudo firewall-cmd --list-all',
  'cat /etc/os-release',
  'cat /proc/version',
  'cat /proc/cpuinfo',
  'cat /proc/meminfo',
  'lscpu',
  'lsblk',
  'mount',
  'sudo fdisk -l',
  'sudo parted -l',
  'crontab -l',
  'sudo crontab -l',
  'history',
  'which',
  'whereis',
  'find / -name',
  'find . -name',
  'grep -r',
  'grep -i',
  'awk',
  'sed',
  'sort',
  'uniq',
  'wc -l',
  'head -n',
  'tail -n',
  'chmod',
  'chown',
  'chgrp',
  'sudo chmod',
  'sudo chown',
  'sudo chgrp',
  'tar -czf',
  'tar -xzf',
  'zip -r',
  'unzip',
  'wget',
  'curl',
  'scp',
  'rsync',
  'ssh',
  'ping',
  'traceroute',
  'nslookup',
  'dig',
  'host'
];

// Message type configurations
export const MESSAGE_TYPE_CONFIG = {
  error: {
    icon: 'AlertTriangle',
    color: 'red',
    priority: 'high' as const,
    borderColor: 'border-red-500'
  },
  warning: {
    icon: 'AlertTriangle',
    color: 'yellow',
    priority: 'medium' as const,
    borderColor: 'border-yellow-400'
  },
  info: {
    icon: 'Info',
    color: 'blue',
    priority: 'low' as const,
    borderColor: 'border-blue-400'
  },
  progress: {
    icon: 'Loader2',
    color: 'blue',
    priority: 'low' as const,
    borderColor: 'border-blue-400'
  },
  command_output: {
    icon: 'Terminal',
    color: 'gray',
    priority: 'low' as const,
    borderColor: 'border-gray-400'
  },
  question: {
    icon: 'HelpCircle',
    color: 'purple',
    priority: 'medium' as const,
    borderColor: 'border-purple-400'
  },
  command_request: {
    icon: 'Bot',
    color: 'teal',
    priority: 'medium' as const,
    borderColor: 'border-teal-400'
  },
  task_start: {
    icon: 'Play',
    color: 'green',
    priority: 'medium' as const,
    borderColor: 'border-green-400'
  },
  task_end: {
    icon: 'CheckCircle',
    color: 'green',
    priority: 'medium' as const,
    borderColor: 'border-green-400'
  },
  summary: {
    icon: 'Sparkles',
    color: 'indigo',
    priority: 'low' as const,
    borderColor: 'border-indigo-400'
  },
  ai_response: {
    icon: 'Bot',
    color: 'teal',
    priority: 'low' as const,
    borderColor: 'border-teal-400'
  },
  security_alert: {
    icon: 'AlertTriangle',
    color: 'red',
    priority: 'critical' as const,
    borderColor: 'border-red-500'
  }
};

// Priority color mappings
export const PRIORITY_COLORS = {
  low: {
    bg: 'bg-gray-100',
    text: 'text-gray-700',
    border: 'border-gray-300'
  },
  medium: {
    bg: 'bg-yellow-100',
    text: 'text-yellow-700',
    border: 'border-yellow-300'
  },
  high: {
    bg: 'bg-orange-100',
    text: 'text-orange-700',
    border: 'border-orange-300'
  },
  critical: {
    bg: 'bg-red-100',
    text: 'text-red-700',
    border: 'border-red-300'
  }
};

// Sender color mappings
export const SENDER_COLORS = {
  user: {
    bubble: 'bg-sky-500 text-white rounded-xl rounded-tr-none shadow-md',
    icon: 'bg-sky-100 text-sky-600'
  },
  ai: {
    bubble: 'bg-gradient-to-br from-teal-500 to-cyan-600 text-white rounded-xl rounded-tl-none shadow-md',
    icon: 'bg-teal-100 text-teal-600'
  },
  system: {
    bubble: 'bg-gray-200 text-gray-700 rounded-xl rounded-tl-none shadow-sm',
    icon: 'bg-gray-300 text-gray-600'
  }
};

// Animation classes
export const ANIMATIONS = {
  fadeIn: 'animate-fadeIn',
  slideIn: 'animate-slideIn',
  pulse: 'animate-pulse',
  spin: 'animate-spin',
  bounce: 'animate-bounce'
};

// Keyboard shortcuts
export const KEYBOARD_SHORTCUTS = {
  SUBMIT: 'Enter',
  HISTORY_UP: 'ArrowUp',
  HISTORY_DOWN: 'ArrowDown',
  CLEAR: 'Escape',
  AUTOCOMPLETE: 'Tab'
};

// Default values
export const DEFAULTS = {
  TASK_STEPS: 5,
  PROGRESS: 0,
  RETRY_COUNT: 0,
  EXECUTION_TIME: 0,
  UPTIME: 0
};

// File export settings
export const EXPORT_SETTINGS = {
  FILE_PREFIX: 'vps-admin-history',
  FILE_EXTENSION: '.json',
  MIME_TYPE: 'application/json',
  INDENT_SPACES: 2
};



// Validation rules
export const VALIDATION = {
  MIN_MESSAGE_LENGTH: 1,
  MAX_MESSAGE_LENGTH: 10000,
  MIN_TASK_TITLE_LENGTH: 3,
  MAX_TASK_TITLE_LENGTH: 100
};
