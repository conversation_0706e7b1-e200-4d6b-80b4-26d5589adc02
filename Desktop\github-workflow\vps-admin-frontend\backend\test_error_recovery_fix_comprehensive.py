#!/usr/bin/env python3
"""
Comprehensive test for the error recovery bug fix.
This script tests the specific scenario from the user's screenshots.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import TaskEntry, TaskStep, StepStatus, SSHResult
from orchestrator import Orchestrator
from agents import ErrorRecoveryPlanner
from ai_client import AIClient
from ssh_client import SSHClient
from task_manager import TaskManager
from config import Config
import json

async def test_git_clone_error_recovery():
    """Test the specific git clone error recovery scenario from the screenshots."""
    print("=== Testing Git Clone Error Recovery Fix ===")
    
    # Initialize components
    config = Config()
    ai_client = AIClient(config)
    ssh_client = SSHClient(config)
    task_manager = TaskManager(config)
    orchestrator = Orchestrator(ai_client, ssh_client, task_manager, config)
    
    # Create a test task similar to the portfolio hosting scenario
    task = TaskEntry(
        id="test-portfolio-fix",
        title="Test Portfolio Hosting Fix",
        description="Host a static web app with error recovery",
        priority="medium",
        history=[],
        chat=None,
        status="EXECUTING",
        command_to_confirm=None,
        system_info="Ubuntu 20.04 LTS, Apache web server available, git installed",
        created_at=asyncio.get_event_loop().time(),
        current_step=0,
        estimated_steps=0,
        commands_executed=0,
        commands_successful=0,
        commands_failed=0,
        recent_commands=[],
        metadata={},
        original_prompt="host this static web app: https://github.com/Moetez-Baklouti/portfolio",
        use_orchestrator=True,
        main_context_history=[],
        current_step_attempts=[],
        task_plan=None,
        final_output="",
        max_retries_per_step=3,
        # Initialize error recovery fields
        in_error_recovery=False,
        recovery_plan=[],
        current_recovery_step=0,
        failed_step_number=0,
        awaiting_step_confirmation=False,
        current_step_command=None,
        current_step_description=None,
        current_step_number=0,
        current_step_attempt=1
    )
    
    # Create a test step for git clone
    step = TaskStep(
        step_number=1,
        description="Clone portfolio repository to web directory",
        command="git clone https://github.com/Moetez-Baklouti/portfolio /var/www/html/portfolio",
        status=StepStatus.EXECUTING,
        summary="",
        attempts=[]
    )
    
    # Simulate the "already exists" error from the screenshots
    ssh_result = SSHResult(
        stdout="",
        stderr="fatal: destination path '/var/www/html/portfolio' already exists and is not an empty directory.",
        exit_status=128,
        success=False,
        command="git clone https://github.com/Moetez-Baklouti/portfolio /var/www/html/portfolio",
        execution_time=1500
    )
    
    print(f"✓ Created test scenario - Command: {step.command}")
    print(f"✓ Simulated error: {ssh_result.stderr}")
    
    # Test 1: Check if error recovery should be triggered
    should_recover = orchestrator._should_trigger_error_recovery(ssh_result)
    print(f"✓ Should trigger recovery: {should_recover}")
    assert should_recover, "Error recovery should be triggered for 'already exists' error"
    
    # Test 2: Test error recovery planning
    print("\n--- Testing Error Recovery Planning ---")
    
    recovery_planner = ErrorRecoveryPlanner(ai_client)
    error_details = {
        "stdout": ssh_result.stdout,
        "stderr": ssh_result.stderr,
        "exit_status": ssh_result.exit_status
    }
    
    try:
        recovery_response = await recovery_planner.process(
            failed_command=step.command,
            error_details=error_details,
            original_objective=step.description
        )
        
        print(f"✓ Recovery planning success: {recovery_response.success}")
        print(f"✓ Recovery approach: {recovery_response.recovery_approach}")
        print(f"✓ Recovery steps: {len(recovery_response.recovery_plan)}")
        
        assert recovery_response.success, "Recovery planning should succeed"
        assert len(recovery_response.recovery_plan) > 0, "Should have recovery steps"
        assert recovery_response.recovery_approach == "cleanup_and_retry", "Should use cleanup_and_retry approach"
        
        # Check if recovery plan contains expected commands
        recovery_commands = [step['command'] for step in recovery_response.recovery_plan]
        print(f"✓ Recovery commands: {recovery_commands}")
        
        # Should contain rm command to clean up existing directory
        has_cleanup = any('rm' in cmd and '/var/www/html/portfolio' in cmd for cmd in recovery_commands)
        assert has_cleanup, "Recovery plan should include cleanup command"
        
        print("✅ Error Recovery Planning PASSED")
        
    except Exception as e:
        print(f"❌ Error Recovery Planning FAILED: {e}")
        return False
    
    # Test 3: Test state management during recovery
    print("\n--- Testing State Management During Recovery ---")
    
    # Simulate the error recovery trigger
    task.in_error_recovery = True
    task.recovery_plan = recovery_response.recovery_plan
    task.current_recovery_step = 0
    task.failed_step_number = step.step_number
    
    # Test confirmation state handling
    task.awaiting_step_confirmation = True
    task.current_step_command = recovery_response.recovery_plan[0]['command']
    task.current_step_description = f"Recovery: {recovery_response.recovery_plan[0]['description']}"
    task.status = "AWAITING_USER_CONFIRMATION"
    
    print(f"✓ Set up recovery state - awaiting_confirmation: {task.awaiting_step_confirmation}")
    print(f"✓ Recovery command: {task.current_step_command}")
    print(f"✓ Task status: {task.status}")
    
    # Test the critical fix: user confirmation handling
    print("\n--- Testing User Confirmation Handling ---")
    
    # Simulate user saying "yes" to recovery command
    events = []
    async for event in orchestrator.handle_user_confirmation(task, "yes"):
        events.append(event)
        event_data = json.loads(event.data)
        print(f"✓ Event: {event_data.get('type')} - {event_data.get('content', '')[:50]}...")
    
    # Check that confirmation state was properly cleared
    assert not task.awaiting_step_confirmation, "Confirmation state should be cleared"
    assert task.status == "EXECUTING", f"Task should be EXECUTING, got {task.status}"
    
    print(f"✓ Confirmation state cleared: {not task.awaiting_step_confirmation}")
    print(f"✓ Task status after confirmation: {task.status}")
    
    # Test 4: Test recovery completion
    print("\n--- Testing Recovery Completion ---")
    
    # Simulate successful recovery completion
    async for event in orchestrator._complete_error_recovery(task, step):
        event_data = json.loads(event.data)
        print(f"✓ Recovery completion event: {event_data.get('type')}")
    
    # Check that all recovery state was cleared
    assert not task.in_error_recovery, "Recovery state should be cleared"
    assert len(task.recovery_plan) == 0, "Recovery plan should be cleared"
    assert task.current_recovery_step == 0, "Recovery step should be reset"
    assert task.failed_step_number == 0, "Failed step number should be reset"
    assert not task.awaiting_step_confirmation, "Confirmation state should be cleared"
    assert task.status == "EXECUTING", f"Task should be EXECUTING, got {task.status}"
    
    print("✅ State Management PASSED")
    
    # Test 5: Test task manager stuck task recovery
    print("\n--- Testing Task Manager Stuck Task Recovery ---")
    
    # Simulate a stuck task scenario
    task.status = "AWAITING_USER_CONFIRMATION"
    task.awaiting_step_confirmation = True
    task.in_error_recovery = True
    task.recovery_plan = [{"command": "test", "description": "test"}]
    
    # Test the enhanced reset_stuck_task method
    success = task_manager.reset_stuck_task(task.id)
    
    assert success, "Should successfully reset stuck task"
    assert task.status == "AWAITING_COMMAND", f"Task should be AWAITING_COMMAND, got {task.status}"
    assert not task.awaiting_step_confirmation, "Confirmation state should be cleared"
    assert not task.in_error_recovery, "Recovery state should be cleared"
    assert len(task.recovery_plan) == 0, "Recovery plan should be cleared"
    
    print("✅ Task Manager Stuck Task Recovery PASSED")
    
    print("\n🎉 ALL TESTS PASSED! Error recovery bug fix is working correctly.")
    return True

async def main():
    """Run the comprehensive error recovery test."""
    try:
        success = await test_git_clone_error_recovery()
        if success:
            print("\n✅ COMPREHENSIVE TEST PASSED")
            print("The error recovery bug fix should resolve the issue shown in the screenshots.")
            print("\nKey improvements:")
            print("1. Better state management during error recovery")
            print("2. Proper clearing of confirmation states")
            print("3. Enhanced stuck task recovery")
            print("4. Reduced timeout for faster recovery")
            print("5. More detailed debugging information")
        else:
            print("\n❌ COMPREHENSIVE TEST FAILED")
            return 1
    except Exception as e:
        print(f"\n❌ TEST ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
