#!/usr/bin/env python3
"""
Utility script to reset stuck tasks in the VPS Admin system.
This script helps break infinite loops and reset tasks to a working state.
"""

import asyncio
import json
import sys
from typing import Optional

from task_manager import Task<PERSON>anager
from config import Config
from models import StepStatus, TaskStatus


class TaskResetUtility:
    """Utility for resetting stuck tasks."""
    
    def __init__(self):
        self.config = Config()
        self.task_manager = TaskManager(self.config)
    
    def list_all_tasks(self):
        """List all tasks and their current status."""
        print("\n=== ALL TASKS ===")
        if not self.task_manager.tasks:
            print("No tasks found.")
            return
        
        for task_id, task in self.task_manager.tasks.items():
            print(f"\nTask ID: {task_id}")
            print(f"  Title: {task.title}")
            print(f"  Status: {task.status}")
            print(f"  Created: {task.created_at}")
            
            if hasattr(task, 'awaiting_step_confirmation'):
                print(f"  Awaiting Confirmation: {task.awaiting_step_confirmation}")
            
            if hasattr(task, 'current_step_attempts'):
                print(f"  Current Step Attempts: {len(task.current_step_attempts)}")
            
            if hasattr(task, 'task_plan') and task.task_plan:
                print(f"  Total Steps: {len(task.task_plan.steps)}")
                print(f"  Current Step: {task.current_step}")
                
                # Show step statuses
                for i, step in enumerate(task.task_plan.steps, 1):
                    status_icon = "✓" if step.status == StepStatus.COMPLETED else "✗" if step.status == StepStatus.FAILED else "⏳" if step.status == StepStatus.EXECUTING else "⏸"
                    print(f"    Step {i}: {status_icon} {step.status} - {step.description[:50]}...")
    
    def find_stuck_tasks(self):
        """Find tasks that appear to be stuck."""
        print("\n=== STUCK TASKS ===")
        stuck_tasks = []
        
        for task_id, task in self.task_manager.tasks.items():
            is_stuck = False
            reasons = []
            
            # Check for infinite loop indicators
            if hasattr(task, 'current_step_attempts') and len(task.current_step_attempts) > 5:
                is_stuck = True
                reasons.append(f"Too many attempts ({len(task.current_step_attempts)})")
            
            if hasattr(task, 'awaiting_step_confirmation') and task.awaiting_step_confirmation:
                if task.status in ["EXECUTING", "AWAITING_USER_CONFIRMATION"]:
                    is_stuck = True
                    reasons.append("Stuck awaiting confirmation")
            
            if task.status == "EXECUTING" and hasattr(task, 'task_plan') and task.task_plan:
                # Check if any step has been executing for too long
                for step in task.task_plan.steps:
                    if step.status == StepStatus.EXECUTING and len(step.attempts) > 3:
                        is_stuck = True
                        reasons.append(f"Step {step.step_number} has too many attempts")
            
            if is_stuck:
                stuck_tasks.append((task_id, task, reasons))
                print(f"\nSTUCK: {task_id}")
                print(f"  Title: {task.title}")
                print(f"  Status: {task.status}")
                print(f"  Reasons: {', '.join(reasons)}")
        
        if not stuck_tasks:
            print("No stuck tasks found.")
        
        return stuck_tasks
    
    def reset_task(self, task_id: str, force: bool = False):
        """Reset a specific task."""
        if task_id not in self.task_manager.tasks:
            print(f"Task {task_id} not found.")
            return False
        
        task = self.task_manager.tasks[task_id]
        
        if not force:
            print(f"\nTask: {task.title}")
            print(f"Status: {task.status}")
            confirm = input("Are you sure you want to reset this task? (y/N): ")
            if confirm.lower() != 'y':
                print("Reset cancelled.")
                return False
        
        print(f"Resetting task {task_id}...")
        
        # Reset task state
        task.status = "AWAITING_COMMAND"
        task.awaiting_step_confirmation = False
        task.current_step_command = None
        task.current_step_description = None
        task.current_step_number = 0
        task.current_step_attempt = 1
        
        # Clear current step attempts
        if hasattr(task, 'current_step_attempts'):
            task.current_step_attempts = []
        
        # Reset error recovery state
        if hasattr(task, 'in_error_recovery'):
            task.in_error_recovery = False
            task.recovery_plan = []
            task.current_recovery_step = 0
            task.failed_step_number = 0
        
        # Reset step statuses to allow retry
        if hasattr(task, 'task_plan') and task.task_plan:
            for step in task.task_plan.steps:
                if step.status == StepStatus.EXECUTING:
                    step.status = StepStatus.PENDING
                step.attempts = []
        
        print(f"✓ Task {task_id} has been reset to AWAITING_COMMAND state.")
        return True
    
    def force_skip_current_step(self, task_id: str):
        """Force skip the current step of a task."""
        if task_id not in self.task_manager.tasks:
            print(f"Task {task_id} not found.")
            return False
        
        task = self.task_manager.tasks[task_id]
        
        if not hasattr(task, 'task_plan') or not task.task_plan:
            print("Task has no plan to skip steps from.")
            return False
        
        current_step_num = task.current_step
        if current_step_num <= 0 or current_step_num > len(task.task_plan.steps):
            print("No valid current step to skip.")
            return False
        
        # Find and skip the current step
        for step in task.task_plan.steps:
            if step.step_number == current_step_num:
                step.status = StepStatus.SKIPPED
                print(f"✓ Skipped step {current_step_num}: {step.description}")
                break
        
        # Reset task state
        task.status = "EXECUTING"
        task.awaiting_step_confirmation = False
        task.current_step_command = None
        task.current_step_description = None
        task.current_step_number = 0
        task.current_step_attempt = 1
        task.current_step_attempts = []
        
        print(f"✓ Task {task_id} ready to continue with next step.")
        return True


def main():
    """Main function for the reset utility."""
    utility = TaskResetUtility()
    
    if len(sys.argv) < 2:
        print("VPS Admin Task Reset Utility")
        print("\nUsage:")
        print("  python reset_stuck_task.py list                    # List all tasks")
        print("  python reset_stuck_task.py stuck                   # Find stuck tasks")
        print("  python reset_stuck_task.py reset <task_id>         # Reset a task")
        print("  python reset_stuck_task.py skip <task_id>          # Skip current step")
        print("  python reset_stuck_task.py force-reset <task_id>   # Force reset without confirmation")
        return
    
    command = sys.argv[1].lower()
    
    if command == "list":
        utility.list_all_tasks()
    
    elif command == "stuck":
        stuck_tasks = utility.find_stuck_tasks()
        if stuck_tasks:
            print(f"\nFound {len(stuck_tasks)} stuck task(s).")
            print("Use 'reset <task_id>' to reset a specific task.")
    
    elif command == "reset":
        if len(sys.argv) < 3:
            print("Please provide a task ID to reset.")
            return
        task_id = sys.argv[2]
        utility.reset_task(task_id)
    
    elif command == "force-reset":
        if len(sys.argv) < 3:
            print("Please provide a task ID to force reset.")
            return
        task_id = sys.argv[2]
        utility.reset_task(task_id, force=True)
    
    elif command == "skip":
        if len(sys.argv) < 3:
            print("Please provide a task ID to skip current step.")
            return
        task_id = sys.argv[2]
        utility.force_skip_current_step(task_id)
    
    else:
        print(f"Unknown command: {command}")
        print("Use 'list', 'stuck', 'reset', 'skip', or 'force-reset'")


if __name__ == "__main__":
    main()
