#!/usr/bin/env python3
"""
Test script to verify the PlannerAgent fixes are working correctly.
"""

import asyncio
from config import Config
from agents import PlannerAgent

async def test_planner_with_portfolio_request():
    """Test the PlannerAgent with the portfolio hosting request."""
    
    print("=== Testing PlannerAgent with Portfolio Request ===")
    
    config = Config()
    planner = PlannerAgent(config)
    
    user_prompt = "host this static web app: https://github.com/<PERSON>-<PERSON>/portfolio"
    system_info = "Ubuntu 20.04 LTS, Apache web server available, git installed"
    
    try:
        print(f"User prompt: {user_prompt}")
        print(f"System info: {system_info}")
        print("\nCalling PlannerAgent...")
        
        response = await planner.process(
            user_prompt=user_prompt,
            system_info=system_info
        )
        
        print(f"\nPlanner Response:")
        print(f"Success: {response.success}")
        print(f"Estimated Steps: {response.estimated_steps}")
        print(f"Number of plan steps: {len(response.plan_steps)}")
        
        if response.metadata.get('error'):
            print(f"Error details: {response.metadata['error']}")
        
        print("\nPlan Steps:")
        for i, step in enumerate(response.plan_steps, 1):
            print(f"  {i}. {step['description']}")
        
        return response.success, len(response.plan_steps) > 0
        
    except Exception as e:
        print(f"Test failed with exception: {e}")
        return False, False

async def main():
    """Run the planner test."""
    success, has_steps = await test_planner_with_portfolio_request()
    
    if success and has_steps:
        print("\n🎉 PlannerAgent is working correctly!")
    elif has_steps:
        print("\n⚠️ PlannerAgent created a plan but with warnings (fallback mode)")
    else:
        print("\n❌ PlannerAgent failed to create any plan")

if __name__ == "__main__":
    asyncio.run(main())
