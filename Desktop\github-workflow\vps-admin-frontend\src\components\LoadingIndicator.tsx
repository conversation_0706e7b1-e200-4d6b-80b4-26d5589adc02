/**
 * Loading indicator component for VPS Admin Chat
 */

import React from 'react';
import { Loader2 } from 'lucide-react';

interface LoadingIndicatorProps {
  message?: string;
  className?: string;
  size?: number;
  variant?: 'dots' | 'spinner';
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  message = "AI is thinking...",
  className = "",
  size = 18,
  variant = 'dots'
}) => {
  if (variant === 'spinner') {
    return (
      <div className={`flex justify-center items-center py-3 self-center animate-fadeIn ${className}`}>
        <Loader2 size={size} className="text-teal-500 animate-spin" />
        <span className="ml-3 text-sm text-gray-500">{message}</span>
      </div>
    );
  }

  // Default dots variant
  return (
    <div className={`flex justify-center items-center py-3 self-center animate-fadeIn ${className}`}>
      {/* Simple 3-dot loader */}
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-teal-500 rounded-full animate-bounce"></div>
        <div className="w-2 h-2 bg-teal-500 rounded-full animate-bounce animation-delay-200"></div>
        <div className="w-2 h-2 bg-teal-500 rounded-full animate-bounce animation-delay-400"></div>
      </div>
      <span className="ml-3 text-sm text-gray-500">{message}</span>
    </div>
  );
};

export default LoadingIndicator;
